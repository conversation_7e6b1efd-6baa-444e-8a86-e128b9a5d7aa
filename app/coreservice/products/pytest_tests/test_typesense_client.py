import pytest
import requests
from unittest.mock import patch, <PERSON><PERSON>, MagicMock
from django.conf import settings
from products.typesense.client import (
    get_typesense_client, check_typesense_health, initialize_typesense_schema
)
from products.typesense.schemas.main import (
    products_schema, categories_schema, subcategories_schema
)
from products.pytest_tests.test_utils import MockTypesenseClient


@pytest.fixture
def mock_typesense_settings():
    """Mock Typesense settings"""
    with patch.object(settings, 'TYPESENSE_API_KEY', 'test-api-key'), \
         patch.object(settings, 'TYPESENSE_HOST', 'localhost'), \
         patch.object(settings, 'TYPESENSE_PORT', '8108'), \
         patch.object(settings, 'TYPESENSE_PROTOCOL', 'http'):
        yield


@pytest.mark.unit
class TestTypesenseClient:
    @patch('products.typesense.client.typesense.Client')
    def test_get_typesense_client_configuration(self, mock_client_class, mock_typesense_settings):
        mock_client_instance = Mock()
        mock_client_class.return_value = mock_client_instance
        
        client = get_typesense_client()
        
        # Verify client was created with correct configuration
        mock_client_class.assert_called_once_with({
            'api_key': 'test-api-key',
            'nodes': [{
                'host': 'localhost',
                'port': '8108',
                'protocol': 'http'
            }],
            'connection_timeout_seconds': 5,
            'retry_interval_seconds': 2,
            'num_retries': 3
        })
        
        assert client == mock_client_instance

    @patch('products.typesense.client.typesense.Client')
    def test_get_typesense_client_returns_instance(self, mock_client_class, mock_typesense_settings):
        mock_client_instance = Mock()
        mock_client_class.return_value = mock_client_instance
        
        client = get_typesense_client()
        
        assert client is not None
        assert client == mock_client_instance

    def test_get_typesense_client_with_different_settings(self):
        with patch.object(settings, 'TYPESENSE_API_KEY', 'different-key'), \
             patch.object(settings, 'TYPESENSE_HOST', 'remote-host'), \
             patch.object(settings, 'TYPESENSE_PORT', '9108'), \
             patch.object(settings, 'TYPESENSE_PROTOCOL', 'https'), \
             patch('products.typesense.client.typesense.Client') as mock_client_class:
            
            mock_client_instance = Mock()
            mock_client_class.return_value = mock_client_instance
            
            client = get_typesense_client()
            
            mock_client_class.assert_called_once_with({
                'api_key': 'different-key',
                'nodes': [{
                    'host': 'remote-host',
                    'port': '9108',
                    'protocol': 'https'
                }],
                'connection_timeout_seconds': 5,
                'retry_interval_seconds': 2,
                'num_retries': 3
            })


@pytest.mark.unit
class TestTypesenseHealthCheck:
    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.os.environ.get')
    def test_check_typesense_health_success(self, mock_env_get, mock_requests_get):
        # Mock environment variables
        env_values = {
            'TYPESENSE_HOST': 'localhost',
            'TYPESENSE_PORT': '8108',
            'TYPESENSE_PROTOCOL': 'http',
            'TYPESENSE_API_KEY': 'test-key'
        }
        mock_env_get.side_effect = lambda key, default: env_values.get(key, default)
        
        # Mock successful health response
        mock_response = Mock()
        mock_response.json.return_value = {'ok': True}
        mock_response.raise_for_status.return_value = None
        mock_requests_get.return_value = mock_response
        
        # Create a mock self object with stdout
        mock_self = Mock()
        mock_self.stdout = Mock()
        
        # Should not raise exception
        check_typesense_health(mock_self)
        
        # Verify request was made correctly
        mock_requests_get.assert_called_with(
            'http://localhost:8108/health',
            headers={'X-TYPESENSE-API-KEY': 'test-key'},
            timeout=5
        )

    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.os.environ.get')
    def test_check_typesense_health_failure(self, mock_env_get, mock_requests_get):
        env_values = {
            'TYPESENSE_HOST': 'localhost',
            'TYPESENSE_PORT': '8108',
            'TYPESENSE_PROTOCOL': 'http',
            'TYPESENSE_API_KEY': 'test-key'
        }
        mock_env_get.side_effect = lambda key, default: env_values.get(key, default)
        
        # Mock failed health response
        mock_response = Mock()
        mock_response.json.return_value = {'ok': False}
        mock_response.raise_for_status.return_value = None
        mock_requests_get.return_value = mock_response
        
        mock_self = Mock()
        mock_self.stdout = Mock()
        
        # Should handle failure gracefully
        check_typesense_health(mock_self)

    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.os.environ.get')
    def test_check_typesense_health_connection_error(self, mock_env_get, mock_requests_get):
        env_values = {
            'TYPESENSE_HOST': 'localhost',
            'TYPESENSE_PORT': '8108',
            'TYPESENSE_PROTOCOL': 'http',
            'TYPESENSE_API_KEY': 'test-key'
        }
        mock_env_get.side_effect = lambda key, default: env_values.get(key, default)
        
        # Mock connection error
        mock_requests_get.side_effect = requests.ConnectionError('Connection failed')
        
        mock_self = Mock()
        mock_self.stdout = Mock()
        
        # Should handle connection error gracefully
        check_typesense_health(mock_self)

    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.os.environ.get')
    def test_check_typesense_health_retry_mechanism(self, mock_env_get, mock_requests_get):
        env_values = {
            'TYPESENSE_HOST': 'localhost',
            'TYPESENSE_PORT': '8108',
            'TYPESENSE_PROTOCOL': 'http',
            'TYPESENSE_API_KEY': 'test-key'
        }
        mock_env_get.side_effect = lambda key, default: env_values.get(key, default)
        
        # Mock multiple failures then success
        mock_requests_get.side_effect = [
            requests.ConnectionError('First failure'),
            requests.ConnectionError('Second failure'),
            Mock(json=lambda: {'ok': True}, raise_for_status=lambda: None)
        ]
        
        mock_self = Mock()
        mock_self.stdout = Mock()
        
        check_typesense_health(mock_self)
        
        # Should have made 3 attempts
        assert mock_requests_get.call_count == 3


@pytest.mark.unit
class TestTypesenseSchemaInitialization:
    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.get_typesense_client')
    def test_initialize_typesense_schema_success(self, mock_get_client, mock_requests_get, mock_typesense_settings):
        # Mock health check success
        mock_response = Mock()
        mock_response.json.return_value = {'ok': True}
        mock_response.raise_for_status.return_value = None
        mock_requests_get.return_value = mock_response
        
        # Mock Typesense client
        mock_client = Mock()
        mock_collections = Mock()
        mock_client.collections = mock_collections
        mock_collections.retrieve.return_value = []  # No existing collections
        mock_get_client.return_value = mock_client
        
        initialize_typesense_schema()
        
        # Verify health check was performed
        mock_requests_get.assert_called_once()
        
        # Verify collections were created
        expected_schemas = [products_schema, categories_schema, subcategories_schema]
        assert mock_collections.create.call_count == len(expected_schemas)
        
        # Verify correct schemas were used
        create_calls = [call[0][0] for call in mock_collections.create.call_args_list]
        for schema in expected_schemas:
            assert schema in create_calls

    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.get_typesense_client')
    def test_initialize_typesense_schema_update_existing(self, mock_get_client, mock_requests_get, mock_typesense_settings):
        # Mock health check success
        mock_response = Mock()
        mock_response.json.return_value = {'ok': True}
        mock_response.raise_for_status.return_value = None
        mock_requests_get.return_value = mock_response
        
        # Mock existing collections
        mock_client = Mock()
        mock_collections = Mock()
        mock_client.collections = mock_collections
        
        # Mock existing collection names
        existing_collections = [
            {'name': 'products'},
            {'name': 'categories'}
        ]
        mock_collections.retrieve.return_value = existing_collections
        
        # Mock collection deletion and creation
        mock_collection_instance = Mock()
        mock_collections.__getitem__.return_value = mock_collection_instance
        
        mock_get_client.return_value = mock_client
        
        initialize_typesense_schema()
        
        # Verify existing collections were deleted and recreated
        assert mock_collection_instance.delete.call_count >= 2  # products and categories
        assert mock_collections.create.call_count == 3  # All three schemas

    @patch('products.typesense.client.requests.get')
    def test_initialize_typesense_schema_health_check_failure(self, mock_requests_get, mock_typesense_settings):
        # Mock health check failure
        mock_requests_get.side_effect = requests.ConnectionError('Connection failed')
        
        # Should handle failure gracefully
        initialize_typesense_schema()

    @patch('products.typesense.client.requests.get')
    @patch('products.typesense.client.get_typesense_client')
    def test_initialize_typesense_schema_client_error(self, mock_get_client, mock_requests_get, mock_typesense_settings):
        # Mock health check success
        mock_response = Mock()
        mock_response.json.return_value = {'ok': True}
        mock_response.raise_for_status.return_value = None
        mock_requests_get.return_value = mock_response
        
        # Mock client error
        mock_get_client.side_effect = Exception('Client creation failed')
        
        # Should handle client error gracefully
        initialize_typesense_schema()


@pytest.mark.unit
class TestTypesenseSchemas:
    def test_products_schema_structure(self):
        assert products_schema['name'] == 'products'
        assert 'fields' in products_schema
        
        # Verify required fields
        field_names = [field['name'] for field in products_schema['fields']]
        required_fields = ['id', 'name', 'sku', 'price', 'is_active', 'facility_id']
        
        for field in required_fields:
            assert field in field_names
        
        # Verify field types
        field_types = {field['name']: field['type'] for field in products_schema['fields']}
        assert field_types['id'] == 'string'
        assert field_types['price'] == 'float'
        assert field_types['is_active'] == 'bool'

    def test_categories_schema_structure(self):
        assert categories_schema['name'] == 'categories'
        assert 'fields' in categories_schema
        
        field_names = [field['name'] for field in categories_schema['fields']]
        required_fields = ['id', 'name', 'code', 'is_active']
        
        for field in required_fields:
            assert field in field_names

    def test_subcategories_schema_structure(self):
        assert subcategories_schema['name'] == 'subcategories'
        assert 'fields' in subcategories_schema
        
        field_names = [field['name'] for field in subcategories_schema['fields']]
        required_fields = ['id', 'name', 'code', 'category_id']
        
        for field in required_fields:
            assert field in field_names

    def test_schema_facet_fields(self):
        # Verify facet fields are properly configured
        products_facet_fields = [
            field['name'] for field in products_schema['fields'] 
            if field.get('facet', False)
        ]
        
        expected_facets = ['brand', 'facility_id', 'facility_name', 'categories', 'subcategories', 'tags']
        for facet in expected_facets:
            assert facet in products_facet_fields

    def test_schema_optional_fields(self):
        # Verify optional fields are properly marked
        products_optional_fields = [
            field['name'] for field in products_schema['fields'] 
            if field.get('optional', False)
        ]
        
        expected_optional = ['brand', 'weight', 'weight_unit', 'image_url', 'thumbnail_url', 'display_alias', 'tags', 'colour']
        for optional_field in expected_optional:
            assert optional_field in products_optional_fields


@pytest.mark.integration
class TestTypesenseIntegration:
    @patch('products.typesense.client.get_typesense_client')
    def test_client_integration_with_mock(self, mock_get_client):
        mock_client = MockTypesenseClient()
        mock_get_client.return_value = mock_client
        
        client = get_typesense_client()
        
        # Test basic operations
        assert hasattr(client, 'collections')
        assert 'products' in client.collections
        assert 'categories' in client.collections
        assert 'subcategories' in client.collections

    @patch('products.typesense.client.get_typesense_client')
    def test_schema_initialization_integration(self, mock_get_client):
        mock_client = MockTypesenseClient()
        mock_get_client.return_value = mock_client
        
        with patch('products.typesense.client.requests.get') as mock_requests:
            mock_response = Mock()
            mock_response.json.return_value = {'ok': True}
            mock_response.raise_for_status.return_value = None
            mock_requests.return_value = mock_response
            
            initialize_typesense_schema()
            
            # Verify mock client received schema operations
            # This would depend on how MockTypesenseClient tracks operations
