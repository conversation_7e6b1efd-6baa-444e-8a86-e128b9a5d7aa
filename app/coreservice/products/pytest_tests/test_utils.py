"""
Test utilities and helper functions for product tests
"""
import tempfile
import os
from io import BytesIO
from PIL import Image
from django.core.files.uploadedfile import SimpleUploadedFile
from django.core.files.base import ContentFile
from unittest.mock import Mock, MagicMock, patch
import json


class MockS3Storage:
    """Mock S3 storage class for testing"""

    def __init__(self):
        self.saved_files = {}
        self.deleted_files = []

    def save(self, name, content, max_length=None):
        """Mock save method"""
        self.saved_files[name] = content
        return name

    def delete(self, name):
        """Mock delete method"""
        self.deleted_files.append(name)
        if name in self.saved_files:
            del self.saved_files[name]

    def exists(self, name):
        """Mock exists method"""
        return name in self.saved_files

    def url(self, name):
        """Mock URL method"""
        return f"https://test-bucket.s3.amazonaws.com/{name}"

    def generate_filename(self, filename):
        """Mock generate_filename method"""
        return filename

    def open(self, name, mode='rb'):
        """Mock open method"""
        if name in self.saved_files:
            return self.saved_files[name]
        # Return a test image
        test_image = Image.new('RGB', (100, 100), color='red')
        image_io = BytesIO()
        test_image.save(image_io, format='JPEG')
        image_io.seek(0)
        return image_io


class MockTypesenseClient:
    """Mock Typesense client for testing"""

    def __init__(self):
        self.collections = {
            'categories': MockTypesenseCollection(),
            'subcategories': MockTypesenseCollection(),
            'products': MockTypesenseCollection()
        }

    def reset(self):
        """Reset all collections"""
        for collection in self.collections.values():
            collection.reset()

    def cleanup_all_records(self):
        """Clean up all records from all collections"""
        total_cleaned = 0
        for collection in self.collections.values():
            total_cleaned += collection.documents.cleanup_all()
        return total_cleaned


class MockTypesenseCollection:
    """Mock Typesense collection"""
    
    def __init__(self):
        self.documents = MockTypesenseDocuments()
    
    def reset(self):
        """Reset documents"""
        self.documents.reset()


class MockTypesenseDocuments:
    """Mock Typesense documents"""

    def __init__(self):
        self.stored_documents = {}
        self.upsert_calls = []
        self.delete_calls = []

    def upsert(self, document):
        """Mock upsert method"""
        doc_id = document.get('id')
        self.stored_documents[doc_id] = document
        self.upsert_calls.append(document)
        return {'id': doc_id}

    def delete(self, doc_id):
        """Mock delete method"""
        if doc_id in self.stored_documents:
            del self.stored_documents[doc_id]
        self.delete_calls.append(doc_id)
        return {'id': doc_id}

    def __getitem__(self, doc_id):
        """Support dictionary-style access for documents[doc_id].delete()"""
        return MockTypesenseDocument(doc_id, self)

    def reset(self):
        """Reset all data"""
        self.stored_documents = {}
        self.upsert_calls = []
        self.delete_calls = []

    def cleanup_all(self):
        """Clean up all stored documents (simulates deleting all records)"""
        deleted_count = len(self.stored_documents)
        for doc_id in list(self.stored_documents.keys()):
            self.delete(doc_id)
        return deleted_count


class MockTypesenseDocument:
    """Mock individual Typesense document for delete operations"""

    def __init__(self, doc_id, documents_collection):
        self.doc_id = doc_id
        self.documents_collection = documents_collection

    def delete(self):
        """Mock delete method for individual document"""
        if self.doc_id in self.documents_collection.stored_documents:
            del self.documents_collection.stored_documents[self.doc_id]
        self.documents_collection.delete_calls.append(self.doc_id)
        return {'id': self.doc_id}


def create_test_image(width=100, height=100, color='red', format='JPEG', name=None):
    """Create a test image file"""
    image = Image.new('RGB', (width, height), color=color)
    image_io = BytesIO()
    image.save(image_io, format=format)
    image_io.seek(0)
    
    filename = name or f'test_image.{format.lower()}'
    return SimpleUploadedFile(
        filename,
        image_io.getvalue(),
        content_type=f'image/{format.lower()}'
    )


def create_test_webp_image(width=200, height=200, name=None):
    """Create a test WebP image"""
    return create_test_image(width, height, 'blue', 'WEBP', name)


class MockImageProcessing:
    """Mock image processing functions"""
    
    def __init__(self):
        self.process_calls = []
        self.cleanup_calls = []
        self.cleanup_old_calls = []
    
    def process_webp_images(self, instance, model_type, target_sizes):
        """Mock process_webp_images"""
        self.process_calls.append({
            'instance': instance,
            'model_type': model_type,
            'target_sizes': target_sizes
        })
    
    def cleanup_images(self, instance, model_type=None):
        """Mock cleanup_images"""
        self.cleanup_calls.append({
            'instance': instance,
            'model_type': model_type
        })
    
    def cleanup_old_images(self, old_instance, new_instance, model_type):
        """Mock cleanup_old_images"""
        self.cleanup_old_calls.append({
            'old_instance': old_instance,
            'new_instance': new_instance,
            'model_type': model_type
        })
    
    def reset(self):
        """Reset all call records"""
        self.process_calls = []
        self.cleanup_calls = []
        self.cleanup_old_calls = []


def assert_typesense_document_structure(document, expected_fields):
    assert isinstance(document, dict), "Document should be a dictionary"
    
    for field in expected_fields:
        assert field in document, f"Field '{field}' missing from document"
    
    try:
        json.dumps(document)
    except (TypeError, ValueError) as e:
        raise AssertionError(f"Document is not JSON serializable: {e}")


def assert_image_field_behavior(instance, field_name, mock_storage):
    """Assert proper image field behavior"""
    image_field = getattr(instance, field_name)
    
    if image_field:
        # Check that the image was saved to storage
        assert any(image_field.name in call[0] for call in mock_storage.save.call_args_list), \
            f"Image {image_field.name} was not saved to storage"


def create_category_with_subcategories(category_data, subcategory_count=2):
    """Create a category with multiple subcategories for testing"""
    from products.models import Category, Subcategory
    
    category = Category.objects.create(**category_data)
    subcategories = []
    
    for i in range(subcategory_count):
        subcategory_data = {
            'name': f'Subcategory {i+1}',
            'description': f'Test subcategory {i+1}',
            'code': f'SUB{i+1:03d}',
            'category': category,
            'is_active': True,
            'thumbnail_url': f'https://example.com/sub{i+1}.jpg'
        }
        subcategories.append(Subcategory.objects.create(**subcategory_data))
    
    return category, subcategories


def create_temporary_image_file():
    """Create a temporary image file for testing file uploads"""
    temp_file = tempfile.NamedTemporaryFile(suffix='.jpg', delete=False)
    image = Image.new('RGB', (100, 100), color='green')
    image.save(temp_file.name, 'JPEG')
    return temp_file.name


def cleanup_temporary_file(file_path):
    """Clean up temporary files"""
    try:
        os.unlink(file_path)
    except OSError:
        pass


class DatabaseStateManager:
    """Helper class to manage database state during tests"""
    
    def __init__(self):
        self.initial_counts = {}
    
    def capture_initial_state(self):
        """Capture initial database state"""
        from products.models import Category, Subcategory
        
        self.initial_counts = {
            'categories': Category.objects.count(),
            'subcategories': Subcategory.objects.count()
        }
    
    def assert_no_database_leakage(self):
        """Assert that no objects were left in the database"""
        from products.models import Category, Subcategory
        
        current_counts = {
            'categories': Category.objects.count(),
            'subcategories': Subcategory.objects.count()
        }
        
        for model_name, initial_count in self.initial_counts.items():
            current_count = current_counts[model_name]
            assert current_count == initial_count, \
                f"{model_name} count changed from {initial_count} to {current_count}"


def validate_category_constraints(category):
    """Validate category model constraints"""
    # Check required fields
    assert category.name, "Category name is required"
    assert category.code, "Category code is required"
    assert category.thumbnail_url, "Category thumbnail_url is required"
    
    # Check field lengths
    assert len(category.name) <= 100, "Category name too long"
    assert len(category.code) <= 50, "Category code too long"
    
    # Check boolean field
    assert isinstance(category.is_active, bool), "is_active should be boolean"


def validate_subcategory_constraints(subcategory):
    """Validate subcategory model constraints"""
    # Check required fields
    assert subcategory.name, "Subcategory name is required"
    assert subcategory.code, "Subcategory code is required"
    assert subcategory.category, "Subcategory category is required"
    assert subcategory.thumbnail_url, "Subcategory thumbnail_url is required"
    
    # Check field lengths
    assert len(subcategory.name) <= 100, "Subcategory name too long"
    assert len(subcategory.code) <= 50, "Subcategory code too long"
    assert len(subcategory.thumbnail_url) <= 512, "Subcategory thumbnail_url too long"
    
    # Check boolean field
    assert isinstance(subcategory.is_active, bool), "is_active should be boolean"
