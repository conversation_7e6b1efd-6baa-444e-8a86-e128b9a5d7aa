import pytest
import decimal
from django.contrib import admin
from django.contrib.admin.sites import AdminSite
from django.contrib.auth import get_user_model
from django.test import RequestFactory
from django.http import HttpRequest
from unittest.mock import Mock, patch
from products.models import Product, Category, Subcategory, ProductImage
from products.admin import (
    CategoryAdmin, SubcategoryAdmin, ProductAdmin, ProductImageAdmin, ProductImageInline
)
from products.pytest_tests.test_utils import create_test_image


User = get_user_model()


@pytest.fixture
def admin_user():
    return User.objects.create_superuser(
        username='admin',
        email='<EMAIL>',
        password='testpass123'
    )


@pytest.fixture
def request_factory():
    return RequestFactory()


@pytest.fixture
def admin_request(request_factory, admin_user):
    request = request_factory.get('/admin/')
    request.user = admin_user
    return request


@pytest.fixture
def sample_category():
    return Category.objects.create(
        name='Test Category',
        code='TEST001',
        thumbnail_url='https://example.com/test.jpg'
    )


@pytest.fixture
def sample_subcategory(sample_category):
    return Subcategory.objects.create(
        name='Test Subcategory',
        code='TESTSUB001',
        category=sample_category,
        thumbnail_url='https://example.com/testsub.jpg'
    )


@pytest.fixture
def sample_product(sample_facility, sample_subcategory):
    product = Product.objects.create(
        name='Test Product',
        sku='TESTPROD001',
        price=decimal.Decimal('99.99'),
        thumbnail_url='https://example.com/product.jpg',
        facility=sample_facility
    )
    product.subcategories.set([sample_subcategory])
    return product


@pytest.fixture
def sample_product_image(sample_product):
    return ProductImage.objects.create(
        product=sample_product,
        priority=1,
        alt_text='Test product image',
        is_primary=True
    )


@pytest.mark.unit
class TestCategoryAdmin:
    def test_category_admin_registration(self, db_setup):
        assert admin.site.is_registered(Category)
        admin_class = admin.site._registry[Category]
        assert isinstance(admin_class, CategoryAdmin)

    def test_category_admin_list_display(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        expected_fields = ('name', 'code', 'is_active', 'has_thumbnail_image')
        
        assert category_admin.list_display == expected_fields

    def test_category_admin_list_filter(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        expected_filters = ('is_active', 'section')
        
        assert category_admin.list_filter == expected_filters

    def test_category_admin_search_fields(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        expected_fields = ('name', 'code', 'description')
        
        assert category_admin.search_fields == expected_fields

    def test_category_admin_readonly_fields(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        expected_fields = ('creation_date', 'updation_date')
        
        assert category_admin.readonly_fields == expected_fields

    def test_category_admin_fieldsets(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        
        assert len(category_admin.fieldsets) == 4
        
        # Test fieldset structure
        fieldset_names = [fieldset[0] for fieldset in category_admin.fieldsets]
        assert None in fieldset_names  # Main fieldset
        assert 'Images' in fieldset_names
        assert 'Status' in fieldset_names
        assert 'Timestamps' in fieldset_names

    def test_category_admin_has_thumbnail_image_method(self, db_setup, sample_category):
        category_admin = CategoryAdmin(Category, AdminSite())
        
        # Test with no image
        result = category_admin.has_thumbnail_image(sample_category)
        assert result is False
        
        # Test with image
        test_image = create_test_image()
        sample_category.thumbnail_image = test_image
        sample_category.save()
        
        result = category_admin.has_thumbnail_image(sample_category)
        assert result is True

    def test_category_admin_has_thumbnail_image_attributes(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        method = category_admin.has_thumbnail_image
        
        assert hasattr(method, 'boolean')
        assert method.boolean is True
        assert hasattr(method, 'short_description')
        assert method.short_description == 'Has Image'


@pytest.mark.unit
class TestSubcategoryAdmin:
    def test_subcategory_admin_registration(self, db_setup):
        assert admin.site.is_registered(Subcategory)
        admin_class = admin.site._registry[Subcategory]
        assert isinstance(admin_class, SubcategoryAdmin)

    def test_subcategory_admin_list_display(self, db_setup):
        subcategory_admin = SubcategoryAdmin(Subcategory, AdminSite())
        expected_fields = ('name', 'category', 'code', 'is_active', 'has_thumbnail_image')
        
        assert subcategory_admin.list_display == expected_fields

    def test_subcategory_admin_list_filter(self, db_setup):
        subcategory_admin = SubcategoryAdmin(Subcategory, AdminSite())
        expected_filters = ('category', 'is_active')
        
        assert subcategory_admin.list_filter == expected_filters

    def test_subcategory_admin_has_thumbnail_image_method(self, db_setup, sample_subcategory):
        subcategory_admin = SubcategoryAdmin(Subcategory, AdminSite())
        
        # Test with no image
        result = subcategory_admin.has_thumbnail_image(sample_subcategory)
        assert result is False
        
        # Test with image
        test_image = create_test_image()
        sample_subcategory.thumbnail_image = test_image
        sample_subcategory.save()
        
        result = subcategory_admin.has_thumbnail_image(sample_subcategory)
        assert result is True

    def test_subcategory_admin_fieldsets_structure(self, db_setup):
        subcategory_admin = SubcategoryAdmin(Subcategory, AdminSite())
        
        assert len(subcategory_admin.fieldsets) == 4
        
        # Verify category field is in main fieldset
        main_fieldset = subcategory_admin.fieldsets[0]
        assert 'category' in main_fieldset[1]['fields']


@pytest.mark.unit
class TestProductImageInline:
    def test_product_image_inline_configuration(self, db_setup):
        inline = ProductImageInline(Product, AdminSite())
        
        assert inline.model == ProductImage
        assert inline.extra == 1
        assert inline.max_num == 10
        assert inline.fields == ('image', 'priority', 'alt_text', 'is_primary')

    def test_product_image_inline_is_tabular(self, db_setup):
        inline = ProductImageInline(Product, AdminSite())
        assert isinstance(inline, admin.TabularInline)


@pytest.mark.unit
class TestProductImageAdmin:
    def test_product_image_admin_registration(self, db_setup):
        assert admin.site.is_registered(ProductImage)
        admin_class = admin.site._registry[ProductImage]
        assert isinstance(admin_class, ProductImageAdmin)

    def test_product_image_admin_list_display(self, db_setup):
        product_image_admin = ProductImageAdmin(ProductImage, AdminSite())
        expected_fields = ('product', 'priority', 'is_primary', 'alt_text')
        
        assert product_image_admin.list_display == expected_fields

    def test_product_image_admin_list_filter(self, db_setup):
        product_image_admin = ProductImageAdmin(ProductImage, AdminSite())
        expected_filters = ('is_primary', 'product__brand')
        
        assert product_image_admin.list_filter == expected_filters

    def test_product_image_admin_search_fields(self, db_setup):
        product_image_admin = ProductImageAdmin(ProductImage, AdminSite())
        expected_fields = ('product__name', 'product__sku', 'alt_text')
        
        assert product_image_admin.search_fields == expected_fields


@pytest.mark.unit
class TestProductAdmin:
    def test_product_admin_registration(self, db_setup):
        assert admin.site.is_registered(Product)
        admin_class = admin.site._registry[Product]
        assert isinstance(admin_class, ProductAdmin)

    def test_product_admin_list_display(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        expected_fields = ('name', 'sku', 'price', 'brand', 'facility', 'is_active', 'has_thumbnail_image', 'image_count')
        
        assert product_admin.list_display == expected_fields

    def test_product_admin_list_filter(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        expected_filters = ('is_active', 'facility', 'brand', 'subcategories')
        
        assert product_admin.list_filter == expected_filters

    def test_product_admin_filter_horizontal(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        expected_fields = ('subcategories',)
        
        assert product_admin.filter_horizontal == expected_fields

    def test_product_admin_inlines(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        
        assert len(product_admin.inlines) == 1
        assert product_admin.inlines[0] == ProductImageInline

    def test_product_admin_has_thumbnail_image_method(self, db_setup, sample_product):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Test with no image
        result = product_admin.has_thumbnail_image(sample_product)
        assert result is False
        
        # Test with image
        test_image = create_test_image()
        sample_product.thumbnail_image = test_image
        sample_product.save()
        
        result = product_admin.has_thumbnail_image(sample_product)
        assert result is True

    def test_product_admin_image_count_method(self, db_setup, sample_product, sample_product_image):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Test with one image
        result = product_admin.image_count(sample_product)
        assert result == 1
        
        # Add another image
        ProductImage.objects.create(
            product=sample_product,
            priority=2,
            alt_text='Second image'
        )
        
        result = product_admin.image_count(sample_product)
        assert result == 2

    def test_product_admin_fieldsets_structure(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        
        assert len(product_admin.fieldsets) == 7
        
        fieldset_names = [fieldset[0] for fieldset in product_admin.fieldsets]
        expected_names = [None, 'Categorization', 'Facility', 'Product Details', 'Images', 'Status', 'Timestamps']
        
        for name in expected_names:
            assert name in fieldset_names

    def test_product_admin_method_attributes(self, db_setup):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Test has_thumbnail_image attributes
        has_thumbnail_method = product_admin.has_thumbnail_image
        assert hasattr(has_thumbnail_method, 'boolean')
        assert has_thumbnail_method.boolean is True
        assert hasattr(has_thumbnail_method, 'short_description')
        assert has_thumbnail_method.short_description == 'Has Thumbnail'
        
        # Test image_count attributes
        image_count_method = product_admin.image_count
        assert hasattr(image_count_method, 'short_description')
        assert image_count_method.short_description == "Images"


@pytest.mark.integration
class TestAdminIntegration:
    def test_category_admin_queryset(self, db_setup, admin_request, sample_category):
        category_admin = CategoryAdmin(Category, AdminSite())
        
        queryset = category_admin.get_queryset(admin_request)
        
        assert sample_category in queryset
        assert queryset.count() >= 1

    def test_product_admin_with_inline(self, db_setup, admin_request, sample_product):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Test that inline is properly configured
        inlines = product_admin.get_inline_instances(admin_request)
        assert len(inlines) == 1
        assert isinstance(inlines[0], ProductImageInline)

    def test_admin_custom_methods_with_real_data(self, db_setup, sample_product):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Create multiple product images
        for i in range(3):
            ProductImage.objects.create(
                product=sample_product,
                priority=i + 1,
                alt_text=f'Image {i + 1}'
            )
        
        # Test image count
        assert product_admin.image_count(sample_product) == 3
        
        # Test thumbnail status
        assert product_admin.has_thumbnail_image(sample_product) is False
        
        # Add thumbnail
        test_image = create_test_image()
        sample_product.thumbnail_image = test_image
        sample_product.save()
        
        assert product_admin.has_thumbnail_image(sample_product) is True


@pytest.mark.edge_cases
class TestAdminEdgeCases:
    def test_admin_methods_with_none_values(self, db_setup):
        category_admin = CategoryAdmin(Category, AdminSite())
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Create objects with minimal data
        category = Category(name='Test', code='TEST')
        
        # Test methods with objects that have no images
        assert category_admin.has_thumbnail_image(category) is False
        
        # Test with None object (edge case)
        try:
            category_admin.has_thumbnail_image(None)
        except AttributeError:
            pass  # Expected behavior

    def test_product_admin_image_count_no_images(self, db_setup, sample_product):
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Product with no images
        assert product_admin.image_count(sample_product) == 0

    def test_admin_fieldsets_field_validation(self, db_setup):
        # Verify all fields in fieldsets exist on models
        category_admin = CategoryAdmin(Category, AdminSite())
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Get all field names from Category model
        category_field_names = [field.name for field in Category._meta.get_fields()]
        
        # Check all fieldset fields exist
        for fieldset in category_admin.fieldsets:
            for field_name in fieldset[1]['fields']:
                assert field_name in category_field_names or field_name in category_admin.readonly_fields
        
        # Same for Product
        product_field_names = [field.name for field in Product._meta.get_fields()]
        
        for fieldset in product_admin.fieldsets:
            for field_name in fieldset[1]['fields']:
                assert field_name in product_field_names or field_name in product_admin.readonly_fields

    def test_inline_field_validation(self, db_setup):
        inline = ProductImageInline(Product, AdminSite())
        
        # Verify all inline fields exist on ProductImage model
        product_image_field_names = [field.name for field in ProductImage._meta.get_fields()]
        
        for field_name in inline.fields:
            assert field_name in product_image_field_names


@pytest.mark.performance
class TestAdminPerformance:
    def test_admin_list_view_performance(self, db_setup, sample_facility):
        # Create multiple products for performance testing
        products = []
        for i in range(20):
            product = Product.objects.create(
                name=f'Performance Product {i}',
                sku=f'PERF{i:03d}',
                price=decimal.Decimal('10.00'),
                thumbnail_url=f'https://example.com/perf{i}.jpg',
                facility=sample_facility
            )
            products.append(product)
        
        product_admin = ProductAdmin(Product, AdminSite())
        
        # Test that list display methods work efficiently
        for product in products[:5]:  # Test subset
            assert product_admin.image_count(product) == 0
            assert product_admin.has_thumbnail_image(product) is False

    def test_admin_queryset_optimization(self, db_setup, admin_request, sample_facility):
        # Create test data
        categories = []
        for i in range(10):
            category = Category.objects.create(
                name=f'Category {i}',
                code=f'CAT{i:03d}',
                thumbnail_url=f'https://example.com/cat{i}.jpg'
            )
            categories.append(category)
        
        category_admin = CategoryAdmin(Category, AdminSite())
        
        # Test queryset retrieval
        queryset = category_admin.get_queryset(admin_request)
        assert queryset.count() == 10
        
        # Verify queryset is optimized (no additional queries for basic operations)
        list(queryset)  # Force evaluation
