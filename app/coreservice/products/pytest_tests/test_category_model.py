import pytest
import threading
from django.db import IntegrityError, transaction
from unittest.mock import patch, Mock
from products.models import Category, Subcategory
from products.pytest_tests.test_utils import (
    MockTypesenseClient, MockImageProcessing, MockS3Storage,
    create_test_image, assert_typesense_document_structure,
    validate_category_constraints
)
from django.db.models.signals import post_save


@pytest.mark.unit
class TestCategoryModelBasics:
    def test_category_creation(self, db_setup, sample_category_data):
        category = Category.objects.create(**sample_category_data)

        assert category.id is not None
        assert category.name == sample_category_data['name']
        assert category.code == sample_category_data['code']
        assert category.is_active == sample_category_data['is_active']
        assert category.thumbnail_url == sample_category_data['thumbnail_url']
        assert category.section == sample_category_data['section']
        validate_category_constraints(category)

        assert str(category) == sample_category_data['name']

        assert category._meta.db_table == 'product_categories'
        assert category._meta.ordering == ['name']
        assert category._meta.verbose_name_plural == 'Categories'

    def test_category_default_values(self, db_setup):
        category = Category.objects.create(
            name="Test Category",
            code="TEST001",
            thumbnail_url="https://example.com/test.jpg"
        )

        assert category.is_active is True
        assert category.description is None
        assert category.section is None
        assert not category.thumbnail_image


@pytest.mark.unit
class TestCategoryValidation:
    def test_unique_code_constraint(self, db_setup, sample_category_data):
        Category.objects.create(**sample_category_data)

        duplicate_data = sample_category_data.copy()
        duplicate_data['name'] = 'Different Name'

        with pytest.raises(IntegrityError):
            Category.objects.create(**duplicate_data)

    def test_field_defaults_and_empty_values(self, db_setup):
        category1 = Category.objects.create(code="EMPTY001")
        assert category1.name == ""
        assert category1.code == "EMPTY001"
        assert category1.thumbnail_url == ""
        assert category1.is_active is True
        assert category1.description is None
        assert category1.section is None

    def test_field_length_constraints(self, db_setup):
        long_name = "x" * 101
        with pytest.raises(Exception):
            Category.objects.create(
                name=long_name,
                code="TEST001",
                thumbnail_url="https://example.com/test.jpg"
            )

        long_code = "x" * 51
        with pytest.raises(Exception):
            Category.objects.create(
                name="Test Category",
                code=long_code,
                thumbnail_url="https://example.com/test.jpg"
            )

        long_section = "x" * 51
        with pytest.raises(Exception):
            Category.objects.create(
                name="Test Category",
                code="TEST001",
                thumbnail_url="https://example.com/test.jpg",
                section=long_section
            )


@pytest.mark.unit
class TestCategoryTypesenseIntegration:
    def test_to_typesense_dict_structure(self, db_setup, sample_category_data):
        category = Category.objects.create(**sample_category_data)
        typesense_dict = category.to_typesense_dict()

        expected_fields = ['id', 'name', 'description', 'code', 'is_active', 'section', 'thumbnail_url']
        assert_typesense_document_structure(typesense_dict, expected_fields)

        assert typesense_dict['id'] == str(category.id)
        assert typesense_dict['name'] == category.name
        assert typesense_dict['description'] == category.description
        assert typesense_dict['code'] == category.code
        assert typesense_dict['is_active'] == category.is_active
        assert typesense_dict['section'] == str(category.section)
        assert typesense_dict['thumbnail_url'] == category.thumbnail_url

    def test_to_typesense_dict_with_none_values(self, db_setup):
        category = Category.objects.create(
            name="Test Category",
            code="TEST001",
            thumbnail_url="https://example.com/test.jpg",
            description=None,
            section=None
        )

        typesense_dict = category.to_typesense_dict()
        assert typesense_dict['description'] is None
        assert typesense_dict['section'] == 'None'


@pytest.mark.signals
class TestCategorySignals:
    def test_typesense_integration_on_create(self, db_setup, sample_category_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)

        documents = mock_client.collections['categories'].documents
        assert len(documents.upsert_calls) == 1

        upserted_doc = documents.upsert_calls[0]
        assert upserted_doc['id'] == str(category.id)
        assert upserted_doc['name'] == category.name
        assert upserted_doc['code'] == category.code

    def test_typesense_integration_on_update(self, db_setup, sample_category_data, sample_subcategory_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )

        mock_client.reset()

        category.name = "Updated Category Name"
        category.save()

        categories_docs = mock_client.collections['categories'].documents
        subcategories_docs = mock_client.collections['subcategories'].documents

        assert len(categories_docs.upsert_calls) >= 1
        assert len(subcategories_docs.upsert_calls) >= 0

    def test_typesense_integration_on_delete(self, db_setup, sample_category_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)
        category_id = str(category.id)

        documents = mock_client.collections['categories'].documents
        documents.delete_calls.clear()

        category.delete()

        assert category_id in documents.delete_calls

    def test_typesense_cleanup_verification(self, db_setup, sample_category_data, typesense_mock):
        """Test to verify that Typesense records are cleaned up after test completion"""
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)

        documents = mock_client.collections['categories'].documents

        assert len(documents.stored_documents) == 1
        assert str(category.id) in documents.stored_documents

    def test_signal_error_handling(self, db_setup, sample_category_data):
        with patch('products.typesense.client.get_typesense_client') as mock_client_func:
            mock_client = Mock()
            mock_client.collections = {'categories': Mock()}
            mock_client.collections['categories'].documents.upsert.side_effect = Exception("Typesense error")
            mock_client_func.return_value = mock_client

            category = Category.objects.create(**sample_category_data)
            assert category.id is not None
            assert Category.objects.filter(id=category.id).exists()


@pytest.mark.image_processing
class TestCategoryImageProcessing:
    def test_image_processing_signal_flow(self, db_setup, sample_category_data):
        mock_processing = MockImageProcessing()
        mock_storage = MockS3Storage()
        test_image = create_test_image()

        with patch('products.models.category.process_webp_images', mock_processing.process_webp_images), \
             patch.object(Category._meta.get_field('thumbnail_image'), 'storage', mock_storage):

            category = Category.objects.create(
                **sample_category_data,
                thumbnail_image=test_image
            )

            assert len(mock_processing.process_calls) == 1
            process_call = mock_processing.process_calls[0]
            assert process_call['instance'] == category
            assert process_call['model_type'] == 'Category'
            assert process_call['target_sizes'] == [(200, 200), (300, 300)]

    def test_image_cleanup_on_update(self, db_setup, sample_category_data):
        mock_processing = MockImageProcessing()
        mock_storage = MockS3Storage()
        test_image1 = create_test_image(name='test1.jpg')
        test_image2 = create_test_image(name='test2.jpg')

        with patch('products.models.category.cleanup_old_images', mock_processing.cleanup_old_images), \
             patch('products.models.category.process_webp_images', mock_processing.process_webp_images), \
             patch.object(Category._meta.get_field('thumbnail_image'), 'storage', mock_storage):

            category = Category.objects.create(
                **sample_category_data,
                thumbnail_image=test_image1
            )

            category.thumbnail_image = test_image2
            category.save()

            assert len(mock_processing.cleanup_old_calls) == 1
            cleanup_call = mock_processing.cleanup_old_calls[0]
            assert cleanup_call['new_instance'] == category
            assert cleanup_call['model_type'] == 'Category'

    def test_image_cleanup_on_delete(self, db_setup, sample_category_data):
        mock_processing = MockImageProcessing()
        mock_storage = MockS3Storage()
        test_image = create_test_image()

        with patch('products.models.category.cleanup_images', mock_processing.cleanup_images), \
             patch('products.models.category.process_webp_images', mock_processing.process_webp_images), \
             patch.object(Category._meta.get_field('thumbnail_image'), 'storage', mock_storage):

            category = Category.objects.create(
                **sample_category_data,
                thumbnail_image=test_image
            )

            category_id = category.id  # Store ID before deletion
            category_code = category.code  # Store code before deletion
            category.delete()

            assert len(mock_processing.cleanup_calls) == 1
            cleanup_call = mock_processing.cleanup_calls[0]
            assert cleanup_call['instance'].code == category_code
            assert cleanup_call['model_type'] == 'Category'

    def test_image_processing_error_handling(self, db_setup, sample_category_data):
        mock_storage = MockS3Storage()
        test_image = create_test_image()

        with patch('products.models.category.process_webp_images') as mock_process, \
             patch.object(Category._meta.get_field('thumbnail_image'), 'storage', mock_storage):

            mock_process.side_effect = Exception("Image processing error")

            with pytest.raises(Exception, match="Image processing error"):
                Category.objects.create(
                    **sample_category_data,
                    thumbnail_image=test_image
                )


@pytest.mark.unit
class TestCategoryEdgeCases:
    def test_category_code_case_sensitivity(self, db_setup, sample_category_data):
        Category.objects.create(**sample_category_data)

        different_case_data = sample_category_data.copy()
        different_case_data['code'] = sample_category_data['code'].lower()
        different_case_data['name'] = 'Different Category'

        category2 = Category.objects.create(**different_case_data)
        assert category2.code != sample_category_data['code']

    def test_category_ordering(self, db_setup):
        Category.objects.create(name="Zebra", code="ZEB001", thumbnail_url="https://example.com/z.jpg")
        Category.objects.create(name="Apple", code="APP001", thumbnail_url="https://example.com/a.jpg")
        Category.objects.create(name="Banana", code="BAN001", thumbnail_url="https://example.com/b.jpg")

        categories = list(Category.objects.all())
        category_names = [cat.name for cat in categories]

        assert category_names == sorted(category_names)

    def test_concurrent_category_creation(self, db_setup, sample_category_data):
        def create_category():
            try:
                return Category.objects.create(**sample_category_data)
            except IntegrityError:
                return None

        category1 = create_category()
        assert category1 is not None

        category2 = create_category()
        assert category2 is None

    def test_category_with_special_characters(self, db_setup):
        special_data = {
            'name': 'Electronics & Gadgets™',
            'description': 'Devices with émojis 🔌 and special chars: @#$%^&*()',
            'code': 'ELEC-001_SPECIAL',
            'is_active': True,
            'thumbnail_url': 'https://example.com/special-chars.jpg?param=value&other=123',
            'section': 'main-section'
        }

        category = Category.objects.create(**special_data)
        assert category.name == special_data['name']
        assert category.description == special_data['description']
        assert category.code == special_data['code']

    def test_category_boolean_field_variations(self, db_setup, sample_category_data):
        category1 = Category.objects.create(**{**sample_category_data, 'is_active': 1, 'code': 'TEST001'})
        assert category1.is_active == True

        category2 = Category.objects.create(**{**sample_category_data, 'is_active': 0, 'code': 'TEST002'})
        assert category2.is_active == False


@pytest.mark.database
class TestCategoryDatabaseOperations:
    def test_category_transaction_rollback(self, db_setup, sample_category_data):
        initial_count = Category.objects.count()

        try:
            with transaction.atomic():
                Category.objects.create(**sample_category_data)
                raise Exception("Forced error")
        except Exception:
            pass

        assert Category.objects.count() == initial_count

    def test_category_soft_delete_simulation(self, db_setup, sample_category_data):
        category = Category.objects.create(**sample_category_data)

        category.is_active = False
        category.save()

        assert Category.objects.filter(id=category.id).exists()
        assert not Category.objects.filter(id=category.id, is_active=True).exists()

        active_categories = Category.objects.filter(is_active=True)
        assert category not in active_categories


@pytest.mark.signals
class TestCategorySignalErrorHandling:
    def test_signal_disconnection_and_reconnection(self, db_setup, sample_category_data):

        signal_calls = []

        def test_signal_handler(sender, instance, created, **kwargs):
            signal_calls.append({'instance': instance, 'created': created})

        post_save.connect(test_signal_handler, sender=Category)

        try:
            category = Category.objects.create(**sample_category_data)
            assert len(signal_calls) == 1
            assert signal_calls[0]['created'] is True

            category.name = "Updated"
            category.save()
            assert len(signal_calls) == 2
            assert signal_calls[1]['created'] is False
        except Exception as e:
            print(f"Exception occurred: {e}")
        finally:
            post_save.disconnect(test_signal_handler, sender=Category)


@pytest.mark.signals
class TestCategorySignalPerformance:
    def test_bulk_operations_signal_efficiency(self, db_setup):
        categories_data = []
        for i in range(10):
            categories_data.append(Category(
                name=f"Category {i}",
                code=f"CAT{i:03d}",
                thumbnail_url=f"https://example.com/cat{i}.jpg",
                is_active=True
            ))

        with patch('products.typesense.client.get_typesense_client') as mock_client_func:
            mock_client = MockTypesenseClient()
            mock_client_func.return_value = mock_client

            Category.objects.bulk_create(categories_data)

            assert Category.objects.count() >= 10

    def test_concurrent_signal_handling(self, db_setup, sample_category_data):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            categories = []
            for i in range(3):
                category_data = sample_category_data.copy()
                category_data['code'] = f"CONC{i:03d}"
                category_data['name'] = f"Concurrent Category {i}"

                category = Category.objects.create(**category_data)
                categories.append(category)

            assert len(categories) == 3
            assert len(set(cat.id for cat in categories)) == 3

            documents = mock_client.collections['categories'].documents
            assert len(documents.upsert_calls) >= 3

