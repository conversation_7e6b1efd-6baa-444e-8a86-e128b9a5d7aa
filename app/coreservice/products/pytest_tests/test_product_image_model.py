import pytest
import decimal
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from unittest.mock import patch, Mock
from products.models import Product, ProductImage, Category, Subcategory
from products.pytest_tests.test_utils import (
    MockTypesenseClient, MockImageProcessing, MockS3Storage,
    create_test_image, assert_typesense_document_structure
)


@pytest.fixture
def sample_product(sample_facility):
    return Product.objects.create(
        name='Test Product for Images',
        sku='TESTIMG001',
        price=decimal.Decimal('99.99'),
        thumbnail_url='https://example.com/test.jpg',
        facility=sample_facility
    )


@pytest.fixture
def sample_product_image_data(sample_product):
    return {
        'product': sample_product,
        'priority': 1,
        'alt_text': 'Test product image',
        'is_primary': True
    }


@pytest.mark.unit
class TestProductImageModelBasics:
    def test_product_image_creation(self, db_setup, sample_product_image_data):
        test_image = create_test_image()
        product_image = ProductImage.objects.create(
            **sample_product_image_data,
            image=test_image
        )
        
        assert product_image.id is not None
        assert product_image.product == sample_product_image_data['product']
        assert product_image.priority == 1
        assert product_image.alt_text == 'Test product image'
        assert product_image.is_primary is True

    def test_product_image_str_representation(self, db_setup, sample_product_image_data):
        product_image = ProductImage.objects.create(**sample_product_image_data)
        expected_str = f"{product_image.product.name} - Image {product_image.priority}"
        assert str(product_image) == expected_str

    def test_product_image_meta_options(self, db_setup):
        assert ProductImage._meta.db_table == 'product_images'
        assert ProductImage._meta.ordering == ['priority', 'id']
        assert ('product', 'priority') in ProductImage._meta.unique_together

    def test_product_image_default_values(self, db_setup, sample_product):
        product_image = ProductImage.objects.create(product=sample_product)
        
        assert product_image.priority == 1
        assert product_image.is_primary is False
        assert product_image.alt_text is None


@pytest.mark.validation
class TestProductImageValidation:
    def test_unique_priority_per_product_constraint(self, db_setup, sample_product_image_data):
        ProductImage.objects.create(**sample_product_image_data)
        
        duplicate_data = sample_product_image_data.copy()
        duplicate_data['alt_text'] = 'Different alt text'
        
        with pytest.raises(IntegrityError):
            ProductImage.objects.create(**duplicate_data)

    def test_same_priority_different_products(self, db_setup, sample_facility):
        product1 = Product.objects.create(
            name='Product 1',
            sku='PROD001',
            price=decimal.Decimal('50.00'),
            thumbnail_url='https://example.com/prod1.jpg',
            facility=sample_facility
        )
        product2 = Product.objects.create(
            name='Product 2',
            sku='PROD002',
            price=decimal.Decimal('60.00'),
            thumbnail_url='https://example.com/prod2.jpg',
            facility=sample_facility
        )
        
        image1 = ProductImage.objects.create(product=product1, priority=1)
        image2 = ProductImage.objects.create(product=product2, priority=1)
        
        assert image1.priority == image2.priority == 1
        assert image1.product != image2.product

    def test_priority_ordering(self, db_setup, sample_product):
        # Create images with different priorities
        image3 = ProductImage.objects.create(product=sample_product, priority=3)
        image1 = ProductImage.objects.create(product=sample_product, priority=1)
        image2 = ProductImage.objects.create(product=sample_product, priority=2)
        
        ordered_images = list(ProductImage.objects.filter(product=sample_product))
        assert ordered_images == [image1, image2, image3]

    def test_boolean_field_validation(self, db_setup, sample_product):
        # Test is_primary field variations
        test_cases = [True, False, 1, 0]
        
        for i, is_primary_value in enumerate(test_cases):
            image = ProductImage.objects.create(
                product=sample_product,
                priority=i + 1,
                is_primary=is_primary_value
            )
            
            expected = bool(is_primary_value)
            assert image.is_primary == expected


@pytest.mark.relationships
class TestProductImageRelationships:
    def test_product_image_relationship(self, db_setup, sample_product_image_data):
        product_image = ProductImage.objects.create(**sample_product_image_data)
        
        assert product_image.product == sample_product_image_data['product']
        assert product_image in product_image.product.product_images.all()

    def test_cascade_deletion_from_product(self, db_setup, sample_product_image_data):
        product_image = ProductImage.objects.create(**sample_product_image_data)
        product = product_image.product
        image_id = product_image.id
        
        product.delete()
        
        assert not ProductImage.objects.filter(id=image_id).exists()

    def test_multiple_images_per_product(self, db_setup, sample_product):
        images_data = [
            {'priority': 1, 'alt_text': 'Primary image', 'is_primary': True},
            {'priority': 2, 'alt_text': 'Secondary image', 'is_primary': False},
            {'priority': 3, 'alt_text': 'Tertiary image', 'is_primary': False},
        ]
        
        created_images = []
        for data in images_data:
            image = ProductImage.objects.create(product=sample_product, **data)
            created_images.append(image)
        
        assert sample_product.product_images.count() == 3
        assert list(sample_product.product_images.all()) == created_images

    def test_related_manager_methods(self, db_setup, sample_product):
        # Test various related manager operations
        image1 = ProductImage.objects.create(product=sample_product, priority=1)
        image2 = ProductImage.objects.create(product=sample_product, priority=2)
        
        # Test filter
        primary_images = sample_product.product_images.filter(priority=1)
        assert list(primary_images) == [image1]
        
        # Test count
        assert sample_product.product_images.count() == 2
        
        # Test exists
        assert sample_product.product_images.filter(priority=1).exists()
        assert not sample_product.product_images.filter(priority=5).exists()


@pytest.mark.image_processing
class TestProductImageProcessing:
    def test_image_processing_on_create(self, db_setup, sample_product_image_data):
        mock_processing = MockImageProcessing()
        test_image = create_test_image()
        
        with patch('products.models.product_image.process_webp_images', mock_processing.process_webp_images):
            product_image = ProductImage.objects.create(
                **sample_product_image_data,
                image=test_image
            )
            
            assert len(mock_processing.process_calls) == 1
            process_call = mock_processing.process_calls[0]
            assert process_call['instance'] == product_image
            assert process_call['model_type'] == 'ProductImage'
            assert process_call['target_sizes'] == [(500, 500), (600, 600), (800, 800)]

    def test_image_cleanup_on_update(self, db_setup, sample_product_image_data):
        mock_processing = MockImageProcessing()
        test_image1 = create_test_image()
        test_image2 = create_test_image()
        
        with patch('products.models.product_image.cleanup_old_images', mock_processing.cleanup_old_images):
            product_image = ProductImage.objects.create(
                **sample_product_image_data,
                image=test_image1
            )
            
            product_image.image = test_image2
            product_image.save()
            
            assert len(mock_processing.cleanup_old_calls) == 1
            cleanup_call = mock_processing.cleanup_old_calls[0]
            assert cleanup_call['new_instance'] == product_image
            assert cleanup_call['model_type'] == 'ProductImage'

    def test_image_cleanup_on_delete(self, db_setup, sample_product_image_data):
        mock_processing = MockImageProcessing()
        test_image = create_test_image()
        
        with patch('products.models.product_image.cleanup_images', mock_processing.cleanup_images):
            product_image = ProductImage.objects.create(
                **sample_product_image_data,
                image=test_image
            )
            
            product_image.delete()
            
            assert len(mock_processing.cleanup_calls) == 1
            cleanup_call = mock_processing.cleanup_calls[0]
            assert cleanup_call['instance'] == product_image
            assert cleanup_call['model_type'] == 'ProductImage'

    def test_image_processing_error_handling(self, db_setup, sample_product_image_data):
        test_image = create_test_image()
        
        with patch('products.models.product_image.process_webp_images') as mock_process:
            mock_process.side_effect = Exception('Image processing error')
            
            # Should not raise exception
            product_image = ProductImage.objects.create(
                **sample_product_image_data,
                image=test_image
            )
            assert product_image.id is not None


@pytest.mark.product_integration
class TestProductImageProductIntegration:
    def test_product_image_url_update_on_create(self, db_setup, sample_product):
        mock_client = MockTypesenseClient()
        
        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            # Create product image
            ProductImage.objects.create(
                product=sample_product,
                priority=1,
                is_primary=True
            )
            
            # Should trigger Product update in Typesense
            documents = mock_client.collections['products'].documents
            assert len(documents.upsert_calls) >= 1

    def test_product_image_url_update_on_delete(self, db_setup, sample_product):
        mock_client = MockTypesenseClient()
        
        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            product_image = ProductImage.objects.create(
                product=sample_product,
                priority=1,
                is_primary=True
            )
            
            # Clear previous calls
            documents = mock_client.collections['products'].documents
            documents.upsert_calls.clear()
            
            product_image.delete()
            
            # Should trigger Product update in Typesense
            assert len(documents.upsert_calls) >= 1

    def test_primary_image_management(self, db_setup, sample_product):
        # Create multiple images, only one should be primary
        image1 = ProductImage.objects.create(
            product=sample_product,
            priority=1,
            is_primary=True
        )
        image2 = ProductImage.objects.create(
            product=sample_product,
            priority=2,
            is_primary=False
        )
        
        # Test primary image identification
        primary_images = sample_product.product_images.filter(is_primary=True)
        assert primary_images.count() == 1
        assert primary_images.first() == image1
        
        # Change primary image
        image1.is_primary = False
        image1.save()
        image2.is_primary = True
        image2.save()
        
        primary_images = sample_product.product_images.filter(is_primary=True)
        assert primary_images.count() == 1
        assert primary_images.first() == image2


@pytest.mark.edge_cases
class TestProductImageEdgeCases:
    def test_product_image_with_maximum_priority(self, db_setup, sample_product):
        # Test with very high priority values
        high_priority_image = ProductImage.objects.create(
            product=sample_product,
            priority=999999,
            alt_text='High priority image'
        )

        assert high_priority_image.priority == 999999

    def test_product_image_with_long_alt_text(self, db_setup, sample_product):
        long_alt_text = 'A' * 255  # Maximum length

        product_image = ProductImage.objects.create(
            product=sample_product,
            priority=1,
            alt_text=long_alt_text
        )

        assert product_image.alt_text == long_alt_text

    def test_product_image_unicode_alt_text(self, db_setup, sample_product):
        unicode_alt_text = 'Spëcîál chäracters & émojis 🎉 in alt text'

        product_image = ProductImage.objects.create(
            product=sample_product,
            priority=1,
            alt_text=unicode_alt_text
        )

        assert product_image.alt_text == unicode_alt_text

    def test_product_image_priority_gaps(self, db_setup, sample_product):
        # Create images with non-sequential priorities
        image1 = ProductImage.objects.create(product=sample_product, priority=1)
        image5 = ProductImage.objects.create(product=sample_product, priority=5)
        image10 = ProductImage.objects.create(product=sample_product, priority=10)

        ordered_images = list(ProductImage.objects.filter(product=sample_product))
        assert ordered_images == [image1, image5, image10]

    def test_product_image_zero_priority(self, db_setup, sample_product):
        # Test edge case with zero priority (should be allowed)
        zero_priority_image = ProductImage.objects.create(
            product=sample_product,
            priority=0
        )

        assert zero_priority_image.priority == 0

    def test_multiple_primary_images_handling(self, db_setup, sample_product):
        # Create multiple images marked as primary (business logic should handle this)
        image1 = ProductImage.objects.create(
            product=sample_product,
            priority=1,
            is_primary=True
        )
        image2 = ProductImage.objects.create(
            product=sample_product,
            priority=2,
            is_primary=True
        )

        # Both should be created successfully (business logic elsewhere should handle conflicts)
        primary_images = sample_product.product_images.filter(is_primary=True)
        assert primary_images.count() == 2
        assert image1 in primary_images
        assert image2 in primary_images


@pytest.mark.performance
class TestProductImagePerformance:
    def test_bulk_product_image_creation(self, db_setup, sample_product):
        images_data = []
        for i in range(50):
            images_data.append(ProductImage(
                product=sample_product,
                priority=i + 1,
                alt_text=f'Bulk image {i}',
                is_primary=(i == 0)
            ))

        created_images = ProductImage.objects.bulk_create(images_data)
        assert len(created_images) == 50
        assert sample_product.product_images.count() == 50

    def test_efficient_product_image_queries(self, db_setup, sample_facility):
        # Create multiple products with images
        products = []
        for i in range(10):
            product = Product.objects.create(
                name=f'Product {i}',
                sku=f'PROD{i:03d}',
                price=decimal.Decimal('50.00'),
                thumbnail_url=f'https://example.com/prod{i}.jpg',
                facility=sample_facility
            )
            products.append(product)

            # Create 3 images per product
            for j in range(3):
                ProductImage.objects.create(
                    product=product,
                    priority=j + 1,
                    alt_text=f'Image {j} for product {i}',
                    is_primary=(j == 0)
                )

        # Test efficient querying with select_related
        images_with_products = ProductImage.objects.select_related('product').filter(
            product__name__startswith='Product'
        )

        assert images_with_products.count() == 30

        # Verify relationships are properly loaded
        for image in images_with_products[:5]:  # Test first 5
            assert image.product.name.startswith('Product')

    def test_complex_filtering_performance(self, db_setup, sample_facility):
        # Create products with various image configurations
        for i in range(20):
            product = Product.objects.create(
                name=f'Filter Test Product {i}',
                sku=f'FTP{i:03d}',
                price=decimal.Decimal('75.00'),
                thumbnail_url=f'https://example.com/ftp{i}.jpg',
                facility=sample_facility
            )

            # Create 1-5 images per product
            num_images = (i % 5) + 1
            for j in range(num_images):
                ProductImage.objects.create(
                    product=product,
                    priority=j + 1,
                    alt_text=f'Filter test image {j}',
                    is_primary=(j == 0)
                )

        # Test complex filtering
        primary_images = ProductImage.objects.filter(
            is_primary=True,
            product__name__startswith='Filter Test Product'
        )
        assert primary_images.count() == 20

        high_priority_images = ProductImage.objects.filter(
            priority__lte=2,
            product__price__gte=decimal.Decimal('70.00')
        )
        assert high_priority_images.count() > 0


@pytest.mark.integration
class TestProductImageIntegration:
    def test_complete_product_image_lifecycle(self, db_setup, sample_product):
        mock_client = MockTypesenseClient()
        mock_processing = MockImageProcessing()
        test_image = create_test_image()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client), \
             patch('products.models.product_image.process_webp_images', mock_processing.process_webp_images), \
             patch('products.models.product_image.cleanup_images', mock_processing.cleanup_images):

            # Create product image
            product_image = ProductImage.objects.create(
                product=sample_product,
                priority=1,
                image=test_image,
                alt_text='Lifecycle test image',
                is_primary=True
            )

            # Verify creation
            assert ProductImage.objects.filter(id=product_image.id).exists()
            assert len(mock_processing.process_calls) == 1

            # Update product image
            product_image.alt_text = 'Updated alt text'
            product_image.priority = 2
            product_image.save()

            updated_image = ProductImage.objects.get(id=product_image.id)
            assert updated_image.alt_text == 'Updated alt text'
            assert updated_image.priority == 2

            # Delete product image
            image_id = product_image.id
            product_image.delete()

            assert not ProductImage.objects.filter(id=image_id).exists()
            assert len(mock_processing.cleanup_calls) == 1

    def test_product_image_with_product_operations(self, db_setup, sample_facility):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            # Create product
            product = Product.objects.create(
                name='Integration Test Product',
                sku='INTEG001',
                price=decimal.Decimal('99.99'),
                thumbnail_url='https://example.com/integ.jpg',
                facility=sample_facility
            )

            # Add images
            image1 = ProductImage.objects.create(
                product=product,
                priority=1,
                alt_text='Primary image',
                is_primary=True
            )
            image2 = ProductImage.objects.create(
                product=product,
                priority=2,
                alt_text='Secondary image',
                is_primary=False
            )

            # Verify relationships
            assert product.product_images.count() == 2
            assert image1.product == product
            assert image2.product == product

            # Test cascade deletion
            product.delete()

            assert not ProductImage.objects.filter(id=image1.id).exists()
            assert not ProductImage.objects.filter(id=image2.id).exists()
