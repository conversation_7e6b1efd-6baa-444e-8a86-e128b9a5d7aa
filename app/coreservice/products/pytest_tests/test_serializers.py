import pytest
import decimal
from django.test import RequestFactory
from rest_framework.test import APIRequestFactory
from products.models import Product, Category, Subcategory, ProductImage
from products.serializers import (
    ProductSerializer, CategorySerializer, SubcategorySerializer, 
    ProductSearchSerializer
)


@pytest.fixture
def api_request_factory():
    return APIRequestFactory()


@pytest.fixture
def sample_category():
    return Category.objects.create(
        name='Electronics',
        code='ELEC001',
        description='Electronic devices and accessories',
        thumbnail_url='https://example.com/electronics.jpg'
    )


@pytest.fixture
def sample_subcategory(sample_category):
    return Subcategory.objects.create(
        name='Smartphones',
        code='SMART001',
        description='Mobile phones and smartphones',
        category=sample_category,
        thumbnail_url='https://example.com/smartphones.jpg'
    )


@pytest.fixture
def sample_product(sample_facility, sample_subcategory):
    product = Product.objects.create(
        name='iPhone 14',
        description='Latest iPhone model',
        sku='IP14001',
        price=decimal.Decimal('999.99'),
        brand='Apple',
        is_active=True,
        thumbnail_url='https://example.com/iphone14.jpg',
        weight=decimal.Decimal('0.172'),
        weight_unit='kg',
        display_alias='iPhone 14 Pro',
        tags=['smartphone', 'apple', 'premium'],
        attributes=[{'color': 'Space Black'}, {'storage': '128GB'}],
        colour='Black',
        facility=sample_facility
    )
    product.subcategories.set([sample_subcategory])
    return product


@pytest.mark.unit
class TestCategorySerializer:
    def test_category_serialization(self, db_setup, sample_category):
        serializer = CategorySerializer(sample_category)
        data = serializer.data
        
        expected_fields = ['id', 'name', 'description', 'code', 'is_active', 'thumbnail_url', 'section']
        for field in expected_fields:
            assert field in data
        
        assert data['name'] == 'Electronics'
        assert data['code'] == 'ELEC001'
        assert data['description'] == 'Electronic devices and accessories'
        assert data['is_active'] is True
        assert data['thumbnail_url'] == 'https://example.com/electronics.jpg'

    def test_category_deserialization_valid(self, db_setup):
        valid_data = {
            'name': 'Home & Garden',
            'code': 'HOME001',
            'description': 'Home and garden products',
            'thumbnail_url': 'https://example.com/home.jpg',
            'is_active': True
        }
        
        serializer = CategorySerializer(data=valid_data)
        assert serializer.is_valid()
        
        category = serializer.save()
        assert category.name == 'Home & Garden'
        assert category.code == 'HOME001'
        assert category.is_active is True

    def test_category_deserialization_invalid(self, db_setup):
        invalid_data = {
            'name': '',  # Required field empty
            'code': 'INVALID',
            'thumbnail_url': 'not-a-valid-url'  # Invalid URL
        }
        
        serializer = CategorySerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors
        assert 'thumbnail_url' in serializer.errors

    def test_category_update(self, db_setup, sample_category):
        update_data = {
            'name': 'Updated Electronics',
            'description': 'Updated description'
        }
        
        serializer = CategorySerializer(sample_category, data=update_data, partial=True)
        assert serializer.is_valid()
        
        updated_category = serializer.save()
        assert updated_category.name == 'Updated Electronics'
        assert updated_category.description == 'Updated description'
        assert updated_category.code == 'ELEC001'  # Unchanged

    def test_category_required_fields(self, db_setup):
        incomplete_data = {
            'name': 'Test Category'
            # Missing required fields: code, thumbnail_url
        }
        
        serializer = CategorySerializer(data=incomplete_data)
        assert not serializer.is_valid()
        assert 'code' in serializer.errors
        assert 'thumbnail_url' in serializer.errors


@pytest.mark.unit
class TestSubcategorySerializer:
    def test_subcategory_serialization(self, db_setup, sample_subcategory):
        serializer = SubcategorySerializer(sample_subcategory)
        data = serializer.data
        
        expected_fields = ['id', 'name', 'description', 'code', 'category', 'is_active', 'thumbnail_url']
        for field in expected_fields:
            assert field in data
        
        assert data['name'] == 'Smartphones'
        assert data['code'] == 'SMART001'
        assert data['category'] == sample_subcategory.category.id
        assert data['is_active'] is True

    def test_subcategory_deserialization_valid(self, db_setup, sample_category):
        valid_data = {
            'name': 'Tablets',
            'code': 'TAB001',
            'description': 'Tablet computers',
            'category': sample_category.id,
            'thumbnail_url': 'https://example.com/tablets.jpg',
            'is_active': True
        }
        
        serializer = SubcategorySerializer(data=valid_data)
        assert serializer.is_valid()
        
        subcategory = serializer.save()
        assert subcategory.name == 'Tablets'
        assert subcategory.category == sample_category

    def test_subcategory_deserialization_invalid_category(self, db_setup):
        invalid_data = {
            'name': 'Test Subcategory',
            'code': 'TEST001',
            'category': 99999,  # Non-existent category
            'thumbnail_url': 'https://example.com/test.jpg'
        }
        
        serializer = SubcategorySerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'category' in serializer.errors

    def test_subcategory_update(self, db_setup, sample_subcategory):
        update_data = {
            'name': 'Updated Smartphones',
            'description': 'Updated smartphone description'
        }
        
        serializer = SubcategorySerializer(sample_subcategory, data=update_data, partial=True)
        assert serializer.is_valid()
        
        updated_subcategory = serializer.save()
        assert updated_subcategory.name == 'Updated Smartphones'
        assert updated_subcategory.category == sample_subcategory.category  # Unchanged


@pytest.mark.unit
class TestProductSerializer:
    def test_product_serialization(self, db_setup, sample_product):
        serializer = ProductSerializer(sample_product)
        data = serializer.data
        
        expected_fields = [
            'id', 'name', 'description', 'sku', 'price', 'brand', 'is_active',
            'image_url', 'weight', 'weight_unit', 'subcategories', 'thumbnail_url',
            'display_alias', 'tags', 'attributes', 'colour', 'facility',
            'subcategory_details', 'facility_name'
        ]
        
        for field in expected_fields:
            assert field in data
        
        assert data['name'] == 'iPhone 14'
        assert data['sku'] == 'IP14001'
        assert float(data['price']) == 999.99
        assert data['brand'] == 'Apple'
        assert data['tags'] == ['smartphone', 'apple', 'premium']
        assert data['facility_name'] == sample_product.facility.name

    def test_product_subcategory_details(self, db_setup, sample_product):
        serializer = ProductSerializer(sample_product)
        data = serializer.data
        
        assert 'subcategory_details' in data
        subcategory_details = data['subcategory_details']
        assert len(subcategory_details) == 1
        
        subcategory_detail = subcategory_details[0]
        assert subcategory_detail['name'] == 'Smartphones'
        assert subcategory_detail['code'] == 'SMART001'

    def test_product_deserialization_valid(self, db_setup, sample_facility, sample_subcategory):
        valid_data = {
            'name': 'Samsung Galaxy S23',
            'description': 'Latest Samsung flagship',
            'sku': 'SGS23001',
            'price': '899.99',
            'brand': 'Samsung',
            'is_active': True,
            'thumbnail_url': 'https://example.com/galaxy.jpg',
            'weight': '0.168',
            'weight_unit': 'kg',
            'subcategories': [sample_subcategory.id],
            'facility': sample_facility.id,
            'tags': ['android', 'samsung'],
            'colour': 'Phantom Black'
        }
        
        serializer = ProductSerializer(data=valid_data)
        assert serializer.is_valid(), serializer.errors
        
        product = serializer.save()
        assert product.name == 'Samsung Galaxy S23'
        assert product.price == decimal.Decimal('899.99')
        assert product.subcategories.count() == 1

    def test_product_deserialization_invalid(self, db_setup):
        invalid_data = {
            'name': '',  # Required field empty
            'sku': 'INVALID',
            'price': 'not-a-number',  # Invalid price
            'thumbnail_url': 'not-a-url'  # Invalid URL
        }
        
        serializer = ProductSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'name' in serializer.errors
        assert 'price' in serializer.errors
        assert 'thumbnail_url' in serializer.errors

    def test_product_update(self, db_setup, sample_product):
        update_data = {
            'name': 'iPhone 14 Pro',
            'price': '1099.99',
            'tags': ['smartphone', 'apple', 'premium', 'pro']
        }
        
        serializer = ProductSerializer(sample_product, data=update_data, partial=True)
        assert serializer.is_valid()
        
        updated_product = serializer.save()
        assert updated_product.name == 'iPhone 14 Pro'
        assert updated_product.price == decimal.Decimal('1099.99')
        assert 'pro' in updated_product.tags

    def test_product_json_fields_validation(self, db_setup, sample_facility, sample_subcategory):
        valid_data = {
            'name': 'JSON Test Product',
            'sku': 'JSON001',
            'price': '99.99',
            'thumbnail_url': 'https://example.com/json.jpg',
            'facility': sample_facility.id,
            'subcategories': [sample_subcategory.id],
            'tags': ['test', 'json'],
            'attributes': [{'key1': 'value1'}, {'key2': 'value2'}],
            'image_url': ['url1.jpg', 'url2.jpg']
        }
        
        serializer = ProductSerializer(data=valid_data)
        assert serializer.is_valid()
        
        product = serializer.save()
        assert product.tags == ['test', 'json']
        assert len(product.attributes) == 2
        assert product.image_url == ['url1.jpg', 'url2.jpg']


@pytest.mark.unit
class TestProductSearchSerializer:
    def test_search_serializer_valid_data(self, db_setup):
        valid_search_data = {
            'q': 'smartphone',
            'category_id': '1',
            'subcategory_id': '2',
            'brand': 'Apple',
            'min_price': '100.00',
            'max_price': '1000.00',
            'is_active': 'true',
            'facility_id': '1',
            'sort_by': 'price',
            'order': 'asc',
            'page': '1',
            'per_page': '20'
        }
        
        serializer = ProductSearchSerializer(data=valid_search_data)
        assert serializer.is_valid()
        
        validated_data = serializer.validated_data
        assert validated_data['q'] == 'smartphone'
        assert validated_data['min_price'] == decimal.Decimal('100.00')
        assert validated_data['max_price'] == decimal.Decimal('1000.00')
        assert validated_data['is_active'] is True
        assert validated_data['page'] == 1
        assert validated_data['per_page'] == 20

    def test_search_serializer_default_values(self, db_setup):
        minimal_data = {'q': 'test'}
        
        serializer = ProductSearchSerializer(data=minimal_data)
        assert serializer.is_valid()
        
        validated_data = serializer.validated_data
        assert validated_data['page'] == 1
        assert validated_data['per_page'] == 10
        assert validated_data['order'] == 'desc'

    def test_search_serializer_invalid_data(self, db_setup):
        invalid_data = {
            'min_price': 'not-a-number',
            'max_price': 'also-not-a-number',
            'page': '0',  # Page should be >= 1
            'per_page': '101',  # Exceeds maximum
            'order': 'invalid_order'  # Not in choices
        }
        
        serializer = ProductSearchSerializer(data=invalid_data)
        assert not serializer.is_valid()
        assert 'min_price' in serializer.errors
        assert 'max_price' in serializer.errors
        assert 'page' in serializer.errors
        assert 'per_page' in serializer.errors
        assert 'order' in serializer.errors

    def test_search_serializer_price_range_validation(self, db_setup):
        # Test that min_price <= max_price validation works if implemented
        data_with_invalid_range = {
            'q': 'test',
            'min_price': '1000.00',
            'max_price': '100.00'  # max < min
        }
        
        serializer = ProductSearchSerializer(data=data_with_invalid_range)
        # This test depends on whether custom validation is implemented
        # For now, just check that it processes the data
        serializer.is_valid()  # May or may not be valid depending on implementation


@pytest.mark.edge_cases
class TestSerializerEdgeCases:
    def test_category_serializer_unicode_handling(self, db_setup):
        unicode_data = {
            'name': 'Électronique & Gadgets 🔌',
            'code': 'ÉLEC001',
            'description': 'Appareils électroniques et accessoires',
            'thumbnail_url': 'https://example.com/électronique.jpg'
        }

        serializer = CategorySerializer(data=unicode_data)
        assert serializer.is_valid()

        category = serializer.save()
        assert '🔌' in category.name
        assert 'électroniques' in category.description

    def test_product_serializer_extreme_values(self, db_setup, sample_facility, sample_subcategory):
        extreme_data = {
            'name': 'A' * 255,  # Maximum length
            'sku': 'B' * 100,   # Maximum length
            'price': '99999999.99',  # Maximum price
            'brand': 'C' * 100,  # Maximum length
            'thumbnail_url': 'https://example.com/extreme.jpg',
            'facility': sample_facility.id,
            'subcategories': [sample_subcategory.id],
            'weight': '999999.99',  # Maximum weight
            'colour': 'D' * 64,  # Maximum length
            'tags': ['tag' + str(i) for i in range(50)],  # Many tags
            'attributes': [{'key' + str(i): 'value' + str(i)} for i in range(20)]  # Many attributes
        }

        serializer = ProductSerializer(data=extreme_data)
        assert serializer.is_valid()

        product = serializer.save()
        assert len(product.name) == 255
        assert len(product.tags) == 50
        assert len(product.attributes) == 20

    def test_serializer_empty_json_fields(self, db_setup, sample_facility, sample_subcategory):
        data_with_empty_json = {
            'name': 'Empty JSON Test',
            'sku': 'EMPTY001',
            'price': '50.00',
            'thumbnail_url': 'https://example.com/empty.jpg',
            'facility': sample_facility.id,
            'subcategories': [sample_subcategory.id],
            'tags': [],
            'attributes': [],
            'image_url': []
        }

        serializer = ProductSerializer(data=data_with_empty_json)
        assert serializer.is_valid()

        product = serializer.save()
        assert product.tags == []
        assert product.attributes == []
        assert product.image_url == []

    def test_serializer_null_optional_fields(self, db_setup, sample_facility, sample_subcategory):
        data_with_nulls = {
            'name': 'Null Fields Test',
            'sku': 'NULL001',
            'price': '25.00',
            'thumbnail_url': 'https://example.com/null.jpg',
            'facility': sample_facility.id,
            'subcategories': [sample_subcategory.id],
            'description': None,
            'brand': None,
            'weight': None,
            'weight_unit': None,
            'display_alias': None,
            'colour': None
        }

        serializer = ProductSerializer(data=data_with_nulls)
        assert serializer.is_valid()

        product = serializer.save()
        assert product.description is None
        assert product.brand is None
        assert product.weight is None


@pytest.mark.validation
class TestSerializerValidation:
    def test_category_code_uniqueness_validation(self, db_setup, sample_category):
        duplicate_code_data = {
            'name': 'Different Category',
            'code': sample_category.code,  # Duplicate code
            'thumbnail_url': 'https://example.com/different.jpg'
        }

        serializer = CategorySerializer(data=duplicate_code_data)
        assert not serializer.is_valid()
        assert 'code' in serializer.errors

    def test_subcategory_code_uniqueness_validation(self, db_setup, sample_subcategory):
        duplicate_code_data = {
            'name': 'Different Subcategory',
            'code': sample_subcategory.code,  # Duplicate code
            'category': sample_subcategory.category.id,
            'thumbnail_url': 'https://example.com/different.jpg'
        }

        serializer = SubcategorySerializer(data=duplicate_code_data)
        assert not serializer.is_valid()
        assert 'code' in serializer.errors

    def test_product_sku_facility_uniqueness_validation(self, db_setup, sample_product):
        duplicate_sku_data = {
            'name': 'Different Product',
            'sku': sample_product.sku,  # Duplicate SKU
            'price': '199.99',
            'thumbnail_url': 'https://example.com/different.jpg',
            'facility': sample_product.facility.id,  # Same facility
            'subcategories': [sample_product.subcategories.first().id]
        }

        serializer = ProductSerializer(data=duplicate_sku_data)
        assert not serializer.is_valid()
        # The exact error field depends on how uniqueness validation is implemented

    def test_product_price_validation(self, db_setup, sample_facility, sample_subcategory):
        invalid_price_cases = [
            {'price': '-10.00', 'case': 'negative_price'},
            {'price': '0.001', 'case': 'too_many_decimals'},
            {'price': '999999999.99', 'case': 'exceeds_max_digits'},
        ]

        for case in invalid_price_cases:
            data = {
                'name': f'Price Test {case["case"]}',
                'sku': f'PRICE{case["case"][:3].upper()}',
                'price': case['price'],
                'thumbnail_url': 'https://example.com/price.jpg',
                'facility': sample_facility.id,
                'subcategories': [sample_subcategory.id]
            }

            serializer = ProductSerializer(data=data)
            # Some cases might be valid depending on business rules
            if not serializer.is_valid():
                assert 'price' in serializer.errors

    def test_url_field_validation(self, db_setup):
        invalid_url_cases = [
            'not-a-url',
            'ftp://invalid-protocol.com',
            'http://',
            'https://',
            'javascript:alert("xss")'
        ]

        for invalid_url in invalid_url_cases:
            data = {
                'name': 'URL Test Category',
                'code': f'URL{hash(invalid_url) % 1000:03d}',
                'thumbnail_url': invalid_url
            }

            serializer = CategorySerializer(data=data)
            assert not serializer.is_valid()
            assert 'thumbnail_url' in serializer.errors


@pytest.mark.performance
class TestSerializerPerformance:
    def test_bulk_serialization_performance(self, db_setup, sample_facility):
        # Create multiple products for bulk serialization
        products = []
        subcategory = Subcategory.objects.create(
            name='Bulk Test Category',
            code='BULK001',
            category=Category.objects.create(
                name='Bulk Category',
                code='BULKCAT',
                thumbnail_url='https://example.com/bulk.jpg'
            ),
            thumbnail_url='https://example.com/bulksub.jpg'
        )

        for i in range(50):
            product = Product.objects.create(
                name=f'Bulk Product {i}',
                sku=f'BULK{i:03d}',
                price=decimal.Decimal('10.00'),
                thumbnail_url=f'https://example.com/bulk{i}.jpg',
                facility=sample_facility
            )
            product.subcategories.set([subcategory])
            products.append(product)

        # Test bulk serialization
        serializer = ProductSerializer(products, many=True)
        data = serializer.data

        assert len(data) == 50
        for item in data[:5]:  # Check first 5
            assert 'name' in item
            assert 'subcategory_details' in item
            assert len(item['subcategory_details']) == 1

    def test_nested_serialization_efficiency(self, db_setup, sample_product):
        # Test that nested serialization (subcategory_details, facility_name) works efficiently
        serializer = ProductSerializer(sample_product)
        data = serializer.data

        # Verify nested data is properly serialized
        assert 'subcategory_details' in data
        assert 'facility_name' in data
        assert data['facility_name'] == sample_product.facility.name

        subcategory_details = data['subcategory_details']
        assert len(subcategory_details) > 0
        assert 'name' in subcategory_details[0]
        assert 'code' in subcategory_details[0]


@pytest.mark.integration
class TestSerializerIntegration:
    def test_product_serializer_with_images(self, db_setup, sample_product):
        # Add product images
        ProductImage.objects.create(
            product=sample_product,
            priority=1,
            alt_text='Primary image',
            is_primary=True
        )
        ProductImage.objects.create(
            product=sample_product,
            priority=2,
            alt_text='Secondary image',
            is_primary=False
        )

        serializer = ProductSerializer(sample_product)
        data = serializer.data

        # Verify product data includes image information
        assert 'image_url' in data
        # Note: Actual image URL handling depends on ProductImage signal implementation

    def test_serializer_with_request_context(self, db_setup, sample_product, api_request_factory):
        request = api_request_factory.get('/api/products/')

        serializer = ProductSerializer(sample_product, context={'request': request})
        data = serializer.data

        # Verify serialization works with request context
        assert 'name' in data
        assert 'sku' in data

    def test_cross_serializer_consistency(self, db_setup, sample_category, sample_subcategory, sample_product):
        # Test that related objects are consistently serialized across different serializers

        # Serialize category
        category_serializer = CategorySerializer(sample_category)
        category_data = category_serializer.data

        # Serialize subcategory
        subcategory_serializer = SubcategorySerializer(sample_subcategory)
        subcategory_data = subcategory_serializer.data

        # Serialize product
        product_serializer = ProductSerializer(sample_product)
        product_data = product_serializer.data

        # Verify consistency
        assert category_data['id'] == subcategory_data['category']
        assert subcategory_data['id'] in [sc['id'] for sc in product_data['subcategory_details']]
