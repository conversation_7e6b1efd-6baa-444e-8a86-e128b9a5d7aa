import pytest
import decimal
from django.urls import reverse
from rest_framework.test import APIClient
from rest_framework import status
from unittest.mock import patch
from products.models import Product, Category, Subcategory
from products.pytest_tests.test_utils import MockTypesenseClient


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def sample_category():
    return Category.objects.create(
        name='Electronics',
        code='ELEC001',
        description='Electronic devices',
        thumbnail_url='https://example.com/electronics.jpg'
    )


@pytest.fixture
def sample_subcategory(sample_category):
    return Subcategory.objects.create(
        name='Smartphones',
        code='SMART001',
        description='Mobile phones',
        category=sample_category,
        thumbnail_url='https://example.com/smartphones.jpg'
    )


@pytest.fixture
def sample_product(sample_facility, sample_subcategory):
    product = Product.objects.create(
        name='iPhone 14',
        description='Latest iPhone',
        sku='IP14001',
        price=decimal.Decimal('999.99'),
        brand='Apple',
        is_active=True,
        thumbnail_url='https://example.com/iphone14.jpg',
        facility=sample_facility
    )
    product.subcategories.set([sample_subcategory])
    return product


@pytest.mark.api
class TestCategoryViewSet:
    def test_category_list(self, db_setup, api_client, sample_category):
        url = reverse('category-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        
        category_data = next((item for item in data if item['id'] == sample_category.id), None)
        assert category_data is not None
        assert category_data['name'] == 'Electronics'
        assert category_data['code'] == 'ELEC001'

    def test_category_detail(self, db_setup, api_client, sample_category):
        url = reverse('category-detail', kwargs={'pk': sample_category.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['id'] == sample_category.id
        assert data['name'] == 'Electronics'
        assert data['code'] == 'ELEC001'

    def test_category_create(self, db_setup, api_client):
        url = reverse('category-list')
        data = {
            'name': 'Home & Garden',
            'code': 'HOME001',
            'description': 'Home and garden products',
            'thumbnail_url': 'https://example.com/home.jpg'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data['name'] == 'Home & Garden'
        assert response_data['code'] == 'HOME001'
        
        # Verify category was created in database
        assert Category.objects.filter(code='HOME001').exists()

    def test_category_update(self, db_setup, api_client, sample_category):
        url = reverse('category-detail', kwargs={'pk': sample_category.id})
        data = {
            'name': 'Updated Electronics',
            'description': 'Updated description'
        }
        
        response = api_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['name'] == 'Updated Electronics'
        assert response_data['description'] == 'Updated description'
        
        # Verify update in database
        updated_category = Category.objects.get(id=sample_category.id)
        assert updated_category.name == 'Updated Electronics'

    def test_category_delete(self, db_setup, api_client, sample_category):
        url = reverse('category-detail', kwargs={'pk': sample_category.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not Category.objects.filter(id=sample_category.id).exists()

    def test_category_invalid_data(self, db_setup, api_client):
        url = reverse('category-list')
        invalid_data = {
            'name': '',  # Required field empty
            'code': '',  # Required field empty
            'thumbnail_url': 'not-a-valid-url'
        }
        
        response = api_client.post(url, invalid_data, format='json')
        
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        errors = response.json()
        assert 'name' in errors
        assert 'code' in errors
        assert 'thumbnail_url' in errors


@pytest.mark.api
class TestSubcategoryViewSet:
    def test_subcategory_list(self, db_setup, api_client, sample_subcategory):
        url = reverse('subcategory-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        
        subcategory_data = next((item for item in data if item['id'] == sample_subcategory.id), None)
        assert subcategory_data is not None
        assert subcategory_data['name'] == 'Smartphones'
        assert subcategory_data['code'] == 'SMART001'

    def test_subcategory_detail(self, db_setup, api_client, sample_subcategory):
        url = reverse('subcategory-detail', kwargs={'pk': sample_subcategory.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['id'] == sample_subcategory.id
        assert data['name'] == 'Smartphones'
        assert data['category'] == sample_subcategory.category.id

    def test_subcategory_create(self, db_setup, api_client, sample_category):
        url = reverse('subcategory-list')
        data = {
            'name': 'Tablets',
            'code': 'TAB001',
            'description': 'Tablet computers',
            'category': sample_category.id,
            'thumbnail_url': 'https://example.com/tablets.jpg'
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data['name'] == 'Tablets'
        assert response_data['category'] == sample_category.id
        
        # Verify subcategory was created
        assert Subcategory.objects.filter(code='TAB001').exists()

    def test_subcategory_filtering_by_category(self, db_setup, api_client, sample_subcategory):
        url = reverse('subcategory-list')
        response = api_client.get(url, {'category_id': sample_subcategory.category.id})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All returned subcategories should belong to the specified category
        for item in data:
            assert item['category'] == sample_subcategory.category.id

    def test_subcategory_update(self, db_setup, api_client, sample_subcategory):
        url = reverse('subcategory-detail', kwargs={'pk': sample_subcategory.id})
        data = {
            'name': 'Updated Smartphones',
            'description': 'Updated smartphone description'
        }
        
        response = api_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['name'] == 'Updated Smartphones'

    def test_subcategory_delete(self, db_setup, api_client, sample_subcategory):
        url = reverse('subcategory-detail', kwargs={'pk': sample_subcategory.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not Subcategory.objects.filter(id=sample_subcategory.id).exists()


@pytest.mark.api
class TestProductViewSet:
    def test_product_list(self, db_setup, api_client, sample_product):
        url = reverse('product-list')
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data) >= 1
        
        product_data = next((item for item in data if item['id'] == sample_product.id), None)
        assert product_data is not None
        assert product_data['name'] == 'iPhone 14'
        assert product_data['sku'] == 'IP14001'
        assert float(product_data['price']) == 999.99

    def test_product_detail(self, db_setup, api_client, sample_product):
        url = reverse('product-detail', kwargs={'pk': sample_product.id})
        response = api_client.get(url)
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['id'] == sample_product.id
        assert data['name'] == 'iPhone 14'
        assert 'subcategory_details' in data
        assert 'facility_name' in data

    def test_product_create(self, db_setup, api_client, sample_facility, sample_subcategory):
        url = reverse('product-list')
        data = {
            'name': 'Samsung Galaxy S23',
            'description': 'Latest Samsung flagship',
            'sku': 'SGS23001',
            'price': '899.99',
            'brand': 'Samsung',
            'thumbnail_url': 'https://example.com/galaxy.jpg',
            'facility': sample_facility.id,
            'subcategories': [sample_subcategory.id]
        }
        
        response = api_client.post(url, data, format='json')
        
        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data['name'] == 'Samsung Galaxy S23'
        assert response_data['sku'] == 'SGS23001'
        
        # Verify product was created
        assert Product.objects.filter(sku='SGS23001').exists()

    def test_product_update(self, db_setup, api_client, sample_product):
        url = reverse('product-detail', kwargs={'pk': sample_product.id})
        data = {
            'name': 'iPhone 14 Pro',
            'price': '1099.99'
        }
        
        response = api_client.patch(url, data, format='json')
        
        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['name'] == 'iPhone 14 Pro'
        assert float(response_data['price']) == 1099.99

    def test_product_delete(self, db_setup, api_client, sample_product):
        url = reverse('product-detail', kwargs={'pk': sample_product.id})
        response = api_client.delete(url)
        
        assert response.status_code == status.HTTP_204_NO_CONTENT
        assert not Product.objects.filter(id=sample_product.id).exists()

    def test_product_filtering_by_facility(self, db_setup, api_client, sample_product):
        url = reverse('product-list')
        response = api_client.get(url, {'facility_id': sample_product.facility.id})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # All returned products should belong to the specified facility
        for item in data:
            assert item['facility'] == sample_product.facility.id

    def test_product_filtering_by_category(self, db_setup, api_client, sample_product):
        category_id = sample_product.subcategories.first().category.id
        url = reverse('product-list')
        response = api_client.get(url, {'category_id': category_id})
        
        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        
        # Verify products are filtered by category
        assert len(data) >= 1
        product_data = next((item for item in data if item['id'] == sample_product.id), None)
        assert product_data is not None


@pytest.mark.api
class TestProductSearch:
    @patch('products.typesense.client.get_typesense_client')
    def test_product_search_basic(self, mock_client, db_setup, api_client, sample_product):
        mock_typesense = MockTypesenseClient()
        mock_client.return_value = mock_typesense

        # Mock search results
        mock_typesense.search_results = {
            'hits': [
                {
                    'document': {
                        'id': str(sample_product.id),
                        'name': sample_product.name,
                        'sku': sample_product.sku,
                        'price': float(sample_product.price)
                    }
                }
            ],
            'found': 1
        }

        url = reverse('product-search')
        response = api_client.get(url, {'q': 'iPhone'})

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert 'results' in data
        assert 'total' in data
        assert data['total'] == 1
        assert len(data['results']) == 1

    @patch('products.typesense.client.get_typesense_client')
    def test_product_search_with_filters(self, mock_client, db_setup, api_client, sample_product):
        mock_typesense = MockTypesenseClient()
        mock_client.return_value = mock_typesense

        mock_typesense.search_results = {
            'hits': [
                {
                    'document': {
                        'id': str(sample_product.id),
                        'name': sample_product.name,
                        'brand': sample_product.brand,
                        'price': float(sample_product.price)
                    }
                }
            ],
            'found': 1
        }

        url = reverse('product-search')
        params = {
            'q': 'smartphone',
            'brand': 'Apple',
            'min_price': '500',
            'max_price': '1500',
            'category_id': sample_product.subcategories.first().category.id
        }
        response = api_client.get(url, params)

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['total'] == 1

    @patch('products.typesense.client.get_typesense_client')
    def test_product_search_pagination(self, mock_client, db_setup, api_client):
        mock_typesense = MockTypesenseClient()
        mock_client.return_value = mock_typesense

        # Mock paginated results
        mock_typesense.search_results = {
            'hits': [
                {'document': {'id': str(i), 'name': f'Product {i}'}}
                for i in range(10)
            ],
            'found': 50
        }

        url = reverse('product-search')
        response = api_client.get(url, {'q': 'test', 'page': '2', 'per_page': '10'})

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['total'] == 50
        assert len(data['results']) == 10

    @patch('products.typesense.client.get_typesense_client')
    def test_product_search_sorting(self, mock_client, db_setup, api_client):
        mock_typesense = MockTypesenseClient()
        mock_client.return_value = mock_typesense

        mock_typesense.search_results = {
            'hits': [
                {'document': {'id': '1', 'name': 'Product A', 'price': 100}},
                {'document': {'id': '2', 'name': 'Product B', 'price': 200}}
            ],
            'found': 2
        }

        url = reverse('product-search')
        response = api_client.get(url, {'q': 'product', 'sort_by': 'price', 'order': 'asc'})

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert len(data['results']) == 2

    @patch('products.typesense.client.get_typesense_client')
    def test_product_search_no_results(self, mock_client, db_setup, api_client):
        mock_typesense = MockTypesenseClient()
        mock_client.return_value = mock_typesense

        mock_typesense.search_results = {
            'hits': [],
            'found': 0
        }

        url = reverse('product-search')
        response = api_client.get(url, {'q': 'nonexistent'})

        assert response.status_code == status.HTTP_200_OK
        data = response.json()
        assert data['total'] == 0
        assert len(data['results']) == 0

    def test_product_search_invalid_params(self, db_setup, api_client):
        url = reverse('product-search')
        response = api_client.get(url, {
            'min_price': 'invalid',
            'max_price': 'also_invalid',
            'page': '0',
            'per_page': '101'
        })

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        errors = response.json()
        assert 'min_price' in errors or 'max_price' in errors


@pytest.mark.api
class TestViewSetErrorHandling:
    def test_category_not_found(self, db_setup, api_client):
        url = reverse('category-detail', kwargs={'pk': 99999})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_subcategory_not_found(self, db_setup, api_client):
        url = reverse('subcategory-detail', kwargs={'pk': 99999})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_product_not_found(self, db_setup, api_client):
        url = reverse('product-detail', kwargs={'pk': 99999})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_category_create_duplicate_code(self, db_setup, api_client, sample_category):
        url = reverse('category-list')
        data = {
            'name': 'Different Category',
            'code': sample_category.code,  # Duplicate code
            'thumbnail_url': 'https://example.com/different.jpg'
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        errors = response.json()
        assert 'code' in errors

    def test_product_create_invalid_facility(self, db_setup, api_client, sample_subcategory):
        url = reverse('product-list')
        data = {
            'name': 'Test Product',
            'sku': 'TEST001',
            'price': '99.99',
            'thumbnail_url': 'https://example.com/test.jpg',
            'facility': 99999,  # Non-existent facility
            'subcategories': [sample_subcategory.id]
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        errors = response.json()
        assert 'facility' in errors

    def test_product_create_invalid_subcategory(self, db_setup, api_client, sample_facility):
        url = reverse('product-list')
        data = {
            'name': 'Test Product',
            'sku': 'TEST001',
            'price': '99.99',
            'thumbnail_url': 'https://example.com/test.jpg',
            'facility': sample_facility.id,
            'subcategories': [99999]  # Non-existent subcategory
        }

        response = api_client.post(url, data, format='json')

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        errors = response.json()
        assert 'subcategories' in errors

    def test_invalid_json_payload(self, db_setup, api_client):
        url = reverse('category-list')

        # Send invalid JSON
        response = api_client.post(
            url,
            'invalid json content',
            content_type='application/json'
        )

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_missing_content_type(self, db_setup, api_client):
        url = reverse('category-list')
        data = {
            'name': 'Test Category',
            'code': 'TEST001',
            'thumbnail_url': 'https://example.com/test.jpg'
        }

        # Send without proper content type
        response = api_client.post(url, data, format='json')

        # Should still work or return appropriate error
        assert response.status_code in [status.HTTP_201_CREATED, status.HTTP_400_BAD_REQUEST]


@pytest.mark.api
class TestViewSetFiltering:
    def test_category_active_filtering(self, db_setup, api_client):
        # Create active and inactive categories
        active_category = Category.objects.create(
            name='Active Category',
            code='ACTIVE001',
            is_active=True,
            thumbnail_url='https://example.com/active.jpg'
        )
        inactive_category = Category.objects.create(
            name='Inactive Category',
            code='INACTIVE001',
            is_active=False,
            thumbnail_url='https://example.com/inactive.jpg'
        )

        url = reverse('category-list')

        # Test filtering by active status
        response = api_client.get(url, {'is_active': 'true'})
        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        active_ids = [item['id'] for item in data if item['is_active']]
        assert active_category.id in active_ids
        assert inactive_category.id not in active_ids

    def test_product_brand_filtering(self, db_setup, api_client, sample_facility, sample_subcategory):
        # Create products with different brands
        apple_product = Product.objects.create(
            name='iPhone',
            sku='APPLE001',
            price=decimal.Decimal('999.99'),
            brand='Apple',
            thumbnail_url='https://example.com/apple.jpg',
            facility=sample_facility
        )
        samsung_product = Product.objects.create(
            name='Galaxy',
            sku='SAMSUNG001',
            price=decimal.Decimal('899.99'),
            brand='Samsung',
            thumbnail_url='https://example.com/samsung.jpg',
            facility=sample_facility
        )

        apple_product.subcategories.set([sample_subcategory])
        samsung_product.subcategories.set([sample_subcategory])

        url = reverse('product-list')
        response = api_client.get(url, {'brand': 'Apple'})

        assert response.status_code == status.HTTP_200_OK
        data = response.json()

        apple_products = [item for item in data if item['brand'] == 'Apple']
        samsung_products = [item for item in data if item['brand'] == 'Samsung']

        assert len(apple_products) >= 1
        assert len(samsung_products) == 0
