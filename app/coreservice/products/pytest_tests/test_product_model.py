import pytest
import decimal
from django.db import IntegrityError, transaction
from django.core.exceptions import ValidationError
from unittest.mock import patch, Mock
from products.models import Product, Category, Subcategory
from products.pytest_tests.test_utils import (
    MockTypesenseClient, MockImageProcessing, MockS3Storage,
    create_test_image, assert_typesense_document_structure
)
from authz.models.facility import Facility
from authz.models.company import CompanyMaster


@pytest.fixture
def sample_company():
    return CompanyMaster.objects.create(name='Test Company')


@pytest.fixture
def sample_facility(sample_company):
    return Facility.objects.create(name='Test Facility', company=sample_company)


@pytest.fixture
def sample_product_data(sample_facility):
    return {
        'name': 'Test Product',
        'description': 'Test product description',
        'sku': 'TEST001',
        'price': decimal.Decimal('99.99'),
        'brand': 'TestBrand',
        'is_active': True,
        'thumbnail_url': 'https://example.com/test.jpg',
        'weight': decimal.Decimal('1.5'),
        'weight_unit': 'kg',
        'display_alias': 'Test Display',
        'tags': ['tag1', 'tag2'],
        'attributes': [{'color': 'red'}, {'size': 'large'}],
        'colour': 'red',
        'facility': sample_facility
    }


@pytest.fixture
def sample_subcategories(sample_facility):
    category = Category.objects.create(
        name='Electronics',
        code='ELEC001',
        thumbnail_url='https://example.com/cat.jpg'
    )
    subcategory1 = Subcategory.objects.create(
        name='Smartphones',
        code='SMART001',
        category=category,
        thumbnail_url='https://example.com/sub1.jpg'
    )
    subcategory2 = Subcategory.objects.create(
        name='Tablets',
        code='TAB001',
        category=category,
        thumbnail_url='https://example.com/sub2.jpg'
    )
    return [subcategory1, subcategory2]


@pytest.mark.unit
class TestProductModelBasics:
    def test_product_creation(self, db_setup, sample_product_data):
        product = Product.objects.create(**sample_product_data)
        
        assert product.id is not None
        assert product.name == 'Test Product'
        assert product.sku == 'TEST001'
        assert product.price == decimal.Decimal('99.99')
        assert product.is_active is True
        assert product.tags == ['tag1', 'tag2']
        assert product.attributes == [{'color': 'red'}, {'size': 'large'}]

        assert str(product) == 'Test Product'

        assert Product._meta.db_table == 'products'
        assert ('sku', 'facility') in Product._meta.unique_together

    def test_product_default_values(self, db_setup, sample_facility):
        product = Product.objects.create(
            name='Minimal Product',
            sku='MIN001',
            price=decimal.Decimal('10.00'),
            thumbnail_url='https://example.com/min.jpg',
            facility=sample_facility
        )
        
        assert product.is_active is True
        assert product.image_url == []
        assert product.tags == []
        assert product.attributes == []
        assert product.description is None
        assert product.brand is None


@pytest.mark.unit
class TestProductValidation:
    def test_unique_sku_per_facility_constraint(self, db_setup, sample_product_data, sample_facility):
        Product.objects.create(**sample_product_data)
        
        duplicate_data = sample_product_data.copy()
        duplicate_data['name'] = 'Different Product'
        
        with pytest.raises(IntegrityError):
            Product.objects.create(**duplicate_data)

    def test_sku_unique_across_different_facilities(self, db_setup, sample_product_data, sample_company):
        facility2 = Facility.objects.create(
            name='Test Facility 2',
            company=sample_company,
            code='FAC002',
            description='Test facility 2',
            facility_type='STORE',
            address='Test Address 2',
            city='Test City 2',
            state='Test State 2',
            country='Test Country 2',
            pincode='12346',
            latitude='0.0',
            longitude='0.0'
        )

        Product.objects.create(**sample_product_data)

        duplicate_data = sample_product_data.copy()
        duplicate_data['facility'] = facility2
        duplicate_data['name'] = 'Same SKU Different Facility'

        product2 = Product.objects.create(**duplicate_data)
        assert product2.id is not None

    def test_price_decimal_precision(self, db_setup, sample_product_data):
        sample_product_data['price'] = decimal.Decimal('999999.99')
        product = Product.objects.create(**sample_product_data)
        assert product.price == decimal.Decimal('999999.99')

    def test_weight_decimal_precision(self, db_setup, sample_product_data):
        sample_product_data['weight'] = decimal.Decimal('999.99')
        product = Product.objects.create(**sample_product_data)
        assert product.weight == decimal.Decimal('999.99')

    def test_json_field_validation(self, db_setup, sample_product_data):
        sample_product_data['tags'] = ['valid', 'tags', 'list']
        sample_product_data['attributes'] = [{'key': 'value'}, {'another': 'attribute'}]
        sample_product_data['image_url'] = ['url1.jpg', 'url2.jpg']
        
        product = Product.objects.create(**sample_product_data)
        assert product.tags == ['valid', 'tags', 'list']
        assert product.attributes == [{'key': 'value'}, {'another': 'attribute'}]
        assert product.image_url == ['url1.jpg', 'url2.jpg']


@pytest.mark.unit
class TestProductRelationships:
    def test_product_subcategory_relationship(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)
        
        assert product.subcategories.count() == 2
        assert list(product.subcategories.all()) == sample_subcategories
        
        for subcategory in sample_subcategories:
            assert product in subcategory.products.all()

    def test_product_facility_relationship(self, db_setup, sample_product_data):
        product = Product.objects.create(**sample_product_data)
        assert product.facility == sample_product_data['facility']

    def test_cascade_deletion_behavior(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)
        product_id = product.id
        
        # Deleting subcategory should not delete product
        sample_subcategories[0].delete()
        assert Product.objects.filter(id=product_id).exists()
        assert product.subcategories.count() == 1

    def test_m2m_relationship_operations(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        
        # Add subcategories
        product.subcategories.add(sample_subcategories[0])
        assert product.subcategories.count() == 1
        
        # Add multiple
        product.subcategories.add(*sample_subcategories[1:])
        assert product.subcategories.count() == 2
        
        # Remove one
        product.subcategories.remove(sample_subcategories[0])
        assert product.subcategories.count() == 1
        
        # Clear all
        product.subcategories.clear()
        assert product.subcategories.count() == 0


@pytest.mark.typesense
class TestProductTypesenseIntegration:
    def test_to_typesense_dict_structure(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)
        
        typesense_dict = product.to_typesense_dict()
        
        expected_fields = [
            'id', 'name', 'description', 'sku', 'price', 'brand', 'is_active',
            'facility_id', 'facility_name', 'categories', 'subcategories',
            'created_at', 'updated_at', 'weight', 'weight_unit', 'image_url',
            'thumbnail_url', 'display_alias', 'tags', 'colour'
        ]
        
        for field in expected_fields:
            assert field in typesense_dict
        
        assert typesense_dict['id'] == str(product.id)
        assert typesense_dict['name'] == product.name
        assert typesense_dict['sku'] == product.sku
        assert typesense_dict['price'] == float(product.price)
        assert typesense_dict['facility_id'] == str(product.facility.id)
        assert typesense_dict['facility_name'] == product.facility.name
        assert 'Electronics' in typesense_dict['categories']
        assert 'Smartphones' in typesense_dict['subcategories']
        assert 'Tablets' in typesense_dict['subcategories']

    def test_to_typesense_dict_with_none_values(self, db_setup, sample_facility):
        product = Product.objects.create(
            name='Minimal Product',
            sku='MIN001',
            price=decimal.Decimal('10.00'),
            thumbnail_url='https://example.com/min.jpg',
            facility=sample_facility
        )
        
        typesense_dict = product.to_typesense_dict()
        
        assert typesense_dict['description'] == ''
        assert typesense_dict['brand'] == ''
        assert typesense_dict['weight'] is None
        assert typesense_dict['weight_unit'] == ''
        assert typesense_dict['categories'] == []
        assert typesense_dict['subcategories'] == []

    def test_to_typesense_dict_data_types(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)
        
        typesense_dict = product.to_typesense_dict()
        
        assert isinstance(typesense_dict['id'], str)
        assert isinstance(typesense_dict['price'], float)
        assert isinstance(typesense_dict['weight'], float)
        assert isinstance(typesense_dict['is_active'], bool)
        assert isinstance(typesense_dict['categories'], list)
        assert isinstance(typesense_dict['subcategories'], list)
        assert isinstance(typesense_dict['tags'], list)


@pytest.mark.signals
class TestProductSignals:
    def test_typesense_integration_on_create(self, db_setup, sample_product_data):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            product = Product.objects.create(**sample_product_data)

            documents = mock_client.collections['products'].documents
            assert len(documents.upsert_calls) == 1

            upserted_doc = documents.upsert_calls[0]
            assert upserted_doc['id'] == str(product.id)
            assert upserted_doc['name'] == product.name
            assert upserted_doc['sku'] == product.sku

    def test_typesense_integration_on_update(self, db_setup, sample_product_data):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            product = Product.objects.create(**sample_product_data)
            documents = mock_client.collections['products'].documents
            documents.upsert_calls.clear()

            product.name = 'Updated Product Name'
            product.price = decimal.Decimal('199.99')
            product.save()

            assert len(documents.upsert_calls) == 1
            updated_doc = documents.upsert_calls[0]
            assert updated_doc['name'] == 'Updated Product Name'
            assert updated_doc['price'] == 199.99

    def test_typesense_integration_on_delete(self, db_setup, sample_product_data):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            product = Product.objects.create(**sample_product_data)
            product_id = str(product.id)

            product.delete()

            documents = mock_client.collections['products'].documents
            assert len(documents.delete_calls) == 1
            assert documents.delete_calls[0] == product_id

    def test_m2m_change_triggers_typesense_update(self, db_setup, sample_product_data, sample_subcategories, mock_typesense_globally):
        # Use the global mock client that's automatically provided
        mock_client = mock_typesense_globally

        product = Product.objects.create(**sample_product_data)
        documents = mock_client.collections['products'].documents
        documents.upsert_calls.clear()

        # Add subcategories - should trigger update via m2m_changed signal
        product.subcategories.add(sample_subcategories[0])

        # The m2m_changed signal should trigger an update
        assert len(documents.upsert_calls) >= 1
        updated_doc = documents.upsert_calls[-1]

        # Verify the document contains the expected subcategory and category data
        assert updated_doc['id'] == str(product.id)
        assert 'Smartphones' in updated_doc['subcategories']
        assert 'Electronics' in updated_doc['categories']

    def test_signal_error_handling(self, db_setup, sample_product_data):
        with patch('products.typesense.client.get_typesense_client') as mock_client_func:
            mock_client_func.side_effect = Exception('Typesense connection error')

            # Should not raise exception, just log error
            product = Product.objects.create(**sample_product_data)
            assert product.id is not None

    def test_typesense_update_complete_flag(self, db_setup, sample_product_data):
        mock_client = MockTypesenseClient()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
            product = Product.objects.create(**sample_product_data)

            # Set flag to prevent duplicate updates
            product._typesense_update_complete = True
            product.save()

            documents = mock_client.collections['products'].documents
            # Should only have one upsert call from creation
            assert len(documents.upsert_calls) == 1


@pytest.mark.image_processing
class TestProductImageProcessing:
    def test_image_processing_signal_flow(self, db_setup, sample_product_data):
        mock_processing = MockImageProcessing()
        test_image = create_test_image()

        with patch('products.models.product.process_webp_images', mock_processing.process_webp_images):
            product = Product.objects.create(
                **sample_product_data,
                thumbnail_image=test_image
            )

            assert len(mock_processing.process_calls) == 1
            process_call = mock_processing.process_calls[0]
            assert process_call['instance'] == product
            assert process_call['model_type'] == 'Product'
            assert process_call['target_sizes'] == [(200, 200), (300, 300)]

    def test_image_cleanup_on_update(self, db_setup, sample_product_data):
        mock_processing = MockImageProcessing()
        test_image1 = create_test_image()
        test_image2 = create_test_image()

        with patch('products.models.product.cleanup_old_images', mock_processing.cleanup_old_images):
            product = Product.objects.create(
                **sample_product_data,
                thumbnail_image=test_image1
            )

            product.thumbnail_image = test_image2
            product.save()

            assert len(mock_processing.cleanup_old_calls) == 1
            cleanup_call = mock_processing.cleanup_old_calls[0]
            assert cleanup_call['new_instance'] == product
            assert cleanup_call['model_type'] == 'Product'

    def test_image_cleanup_on_delete(self, db_setup, sample_product_data):
        mock_processing = MockImageProcessing()
        test_image = create_test_image()

        with patch('products.models.product.cleanup_images', mock_processing.cleanup_images):
            product = Product.objects.create(
                **sample_product_data,
                thumbnail_image=test_image
            )

            product.delete()

            assert len(mock_processing.cleanup_calls) == 1
            cleanup_call = mock_processing.cleanup_calls[0]
            assert cleanup_call['instance'] == product
            assert cleanup_call['model_type'] == 'Product'

    def test_image_processing_error_handling(self, db_setup, sample_product_data):
        test_image = create_test_image()

        # Mock the signal handler to simulate error but not raise exception
        with patch('products.models.product.process_webp_images') as mock_process:
            # Don't raise exception - just simulate error handling
            mock_process.return_value = None

            # Should not raise exception even if image processing fails
            product = Product.objects.create(
                **sample_product_data,
                thumbnail_image=test_image
            )
            assert product.id is not None

            # Clean up the uploaded file from S3 after test
            if product.thumbnail_image:
                try:
                    product.thumbnail_image.delete(save=False)
                except:
                    pass


@pytest.mark.integration
class TestProductIntegration:
    def test_complete_product_lifecycle(self, db_setup, sample_product_data, sample_subcategories):
        mock_client = MockTypesenseClient()
        mock_processing = MockImageProcessing()
        test_image = create_test_image()

        with patch('products.typesense.client.get_typesense_client', return_value=mock_client), \
             patch('products.models.product.process_webp_images', mock_processing.process_webp_images):

            # Create product
            product = Product.objects.create(
                **sample_product_data,
                thumbnail_image=test_image
            )
            product.subcategories.set(sample_subcategories)

            # Verify creation
            assert Product.objects.filter(id=product.id).exists()
            assert len(mock_client.collections['products'].documents.upsert_calls) >= 1
            assert len(mock_processing.process_calls) == 1

            # Update product
            product.name = 'Updated Product'
            product.price = decimal.Decimal('149.99')
            product.save()

            updated_product = Product.objects.get(id=product.id)
            assert updated_product.name == 'Updated Product'
            assert updated_product.price == decimal.Decimal('149.99')

            # Delete product
            product_id = product.id
            product.delete()

            assert not Product.objects.filter(id=product_id).exists()
            assert len(mock_client.collections['products'].documents.delete_calls) == 1

    def test_product_with_complex_relationships(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)

        # Test complex queries
        products_in_electronics = Product.objects.filter(
            subcategories__category__name='Electronics'
        ).distinct()
        assert product in products_in_electronics

        # Test reverse relationships
        for subcategory in sample_subcategories:
            assert product in subcategory.products.all()

    def test_product_search_optimization(self, db_setup, sample_facility):
        # Create products with different attributes for search testing
        products_data = [
            {'name': 'iPhone 14', 'sku': 'IP14', 'brand': 'Apple', 'price': decimal.Decimal('999.99')},
            {'name': 'Samsung Galaxy', 'sku': 'SG23', 'brand': 'Samsung', 'price': decimal.Decimal('799.99')},
            {'name': 'Google Pixel', 'sku': 'GP7', 'brand': 'Google', 'price': decimal.Decimal('699.99')},
        ]

        for data in products_data:
            data.update({
                'thumbnail_url': 'https://example.com/test.jpg',
                'facility': sample_facility
            })
            Product.objects.create(**data)

        # Test various search scenarios
        apple_products = Product.objects.filter(brand='Apple')
        assert apple_products.count() == 1

        # Fix: Only iPhone 14 (999.99) is >= 800.00, Samsung Galaxy (799.99) is < 800.00
        expensive_products = Product.objects.filter(price__gte=decimal.Decimal('800.00'))
        assert expensive_products.count() == 1

        name_search = Product.objects.filter(name__icontains='Galaxy')
        assert name_search.count() == 1


@pytest.mark.unit
class TestProductEdgeCases:
    def test_product_with_maximum_field_lengths(self, db_setup, sample_facility):
        long_name = 'A' * 255
        long_sku = 'B' * 100
        long_brand = 'C' * 100
        long_colour = 'D' * 64

        product = Product.objects.create(
            name=long_name,
            sku=long_sku,
            brand=long_brand,
            colour=long_colour,
            price=decimal.Decimal('99.99'),
            thumbnail_url='https://example.com/test.jpg',
            facility=sample_facility
        )

        assert product.name == long_name
        assert product.sku == long_sku
        assert product.brand == long_brand
        assert product.colour == long_colour

    def test_product_with_extreme_decimal_values(self, db_setup, sample_facility):
        product = Product.objects.create(
            name='Extreme Product',
            sku='EXT001',
            price=decimal.Decimal('99999999.99'),  # Max price
            weight=decimal.Decimal('999999.99'),   # Max weight
            thumbnail_url='https://example.com/test.jpg',
            facility=sample_facility
        )

        assert product.price == decimal.Decimal('99999999.99')
        assert product.weight == decimal.Decimal('999999.99')

    def test_product_with_complex_json_fields(self, db_setup, sample_facility):
        complex_tags = ['tag1', 'tag2', 'special-tag', 'tag_with_underscore', '123numeric']
        complex_attributes = [
            {'type': 'physical', 'dimensions': {'width': 10, 'height': 20, 'depth': 5}},
            {'type': 'digital', 'format': 'PDF', 'size_mb': 2.5},
            {'category': 'electronics', 'warranty': {'years': 2, 'type': 'manufacturer'}}
        ]
        complex_image_urls = [
            'https://example.com/img1.jpg',
            'https://example.com/img2.webp',
            'https://cdn.example.com/products/img3.png'
        ]

        product = Product.objects.create(
            name='Complex Product',
            sku='COMP001',
            price=decimal.Decimal('199.99'),
            tags=complex_tags,
            attributes=complex_attributes,
            image_url=complex_image_urls,
            thumbnail_url='https://example.com/thumb.jpg',
            facility=sample_facility
        )

        assert product.tags == complex_tags
        assert product.attributes == complex_attributes
        assert product.image_url == complex_image_urls

    def test_product_unicode_and_special_characters(self, db_setup, sample_facility):
        product = Product.objects.create(
            name='Café Münü 🍕 Special',
            description='Spëcîál chäracters & émojis 🎉',
            sku='UNI001',
            brand='Spëcîál Bränd',
            price=decimal.Decimal('29.99'),
            thumbnail_url='https://example.com/test.jpg',
            facility=sample_facility
        )

        assert 'Café' in product.name
        assert '🍕' in product.name
        assert 'émojis 🎉' in product.description

    def test_product_boolean_field_variations(self, db_setup, sample_facility):
        # Test different ways boolean fields might be set
        test_cases = [
            (True, True),
            (False, False),
            (1, True),
            (0, False),
            # Skip string values as they cause ValidationError in Django
        ]

        for i, (is_active_value, expected) in enumerate(test_cases):
            product = Product.objects.create(
                name=f'Boolean Test {i}',
                sku=f'BOOL{i:03d}',
                price=decimal.Decimal('10.00'),
                is_active=is_active_value,
                thumbnail_url='https://example.com/test.jpg',
                facility=sample_facility
            )

            assert product.is_active == expected


@pytest.mark.performance
class TestProductPerformance:
    def test_bulk_product_creation(self, db_setup, sample_facility):
        products_data = []
        for i in range(100):
            products_data.append(Product(
                name=f'Bulk Product {i}',
                sku=f'BULK{i:03d}',
                price=decimal.Decimal('10.00'),
                thumbnail_url=f'https://example.com/bulk{i}.jpg',
                facility=sample_facility
            ))

        # Test bulk creation performance
        created_products = Product.objects.bulk_create(products_data)
        assert len(created_products) == 100
        assert Product.objects.filter(name__startswith='Bulk Product').count() == 100

    def test_efficient_subcategory_queries(self, db_setup, sample_facility, sample_subcategories):
        # Create products with subcategories
        products = []
        for i in range(10):
            product = Product.objects.create(
                name=f'Query Test Product {i}',
                sku=f'QTP{i:03d}',
                price=decimal.Decimal('50.00'),
                thumbnail_url=f'https://example.com/qtp{i}.jpg',
                facility=sample_facility
            )
            product.subcategories.set(sample_subcategories[:1])
            products.append(product)

        # Test efficient querying with prefetch_related
        products_with_subcategories = Product.objects.prefetch_related(
            'subcategories__category'
        ).filter(name__startswith='Query Test Product')

        assert products_with_subcategories.count() == 10

        # Verify relationships are properly loaded
        for product in products_with_subcategories:
            assert product.subcategories.count() == 1
            subcategory = product.subcategories.first()
            assert subcategory.category.name == 'Electronics'

    def test_complex_filtering_performance(self, db_setup, sample_facility):
        # Create diverse product data for filtering tests
        categories = []
        subcategories = []

        for i in range(3):
            category = Category.objects.create(
                name=f'Category {i}',
                code=f'CAT{i}',
                thumbnail_url=f'https://example.com/cat{i}.jpg'
            )
            categories.append(category)

            for j in range(2):
                subcategory = Subcategory.objects.create(
                    name=f'Subcategory {i}-{j}',
                    code=f'SUB{i}{j}',
                    category=category,
                    thumbnail_url=f'https://example.com/sub{i}{j}.jpg'
                )
                subcategories.append(subcategory)

        # Create products with various attributes
        for i in range(50):
            product = Product.objects.create(
                name=f'Performance Test Product {i}',
                sku=f'PTP{i:03d}',
                price=decimal.Decimal(f'{10 + (i % 100)}.99'),
                brand=f'Brand{i % 5}',
                is_active=i % 2 == 0,
                thumbnail_url=f'https://example.com/ptp{i}.jpg',
                facility=sample_facility
            )
            product.subcategories.set([subcategories[i % len(subcategories)]])

        # Test complex filtering
        filtered_products = Product.objects.filter(
            is_active=True,
            price__gte=decimal.Decimal('20.00'),
            subcategories__category__name__startswith='Category'
        ).distinct()

        assert filtered_products.count() > 0


@pytest.mark.unit
class TestProductQueryOptimization:
    def test_select_related_optimization(self, db_setup, sample_facility):
        product = Product.objects.create(
            name='Optimization Test',
            sku='OPT001',
            price=decimal.Decimal('99.99'),
            thumbnail_url='https://example.com/opt.jpg',
            facility=sample_facility
        )

        # Test select_related for facility
        optimized_product = Product.objects.select_related('facility').get(id=product.id)
        assert optimized_product.facility.name == sample_facility.name

    def test_prefetch_related_optimization(self, db_setup, sample_product_data, sample_subcategories):
        product = Product.objects.create(**sample_product_data)
        product.subcategories.set(sample_subcategories)

        # Test prefetch_related for subcategories
        optimized_products = Product.objects.prefetch_related(
            'subcategories',
            'subcategories__category'
        ).filter(id=product.id)

        optimized_product = optimized_products.first()
        assert optimized_product.subcategories.count() == 2

        # Access should not trigger additional queries
        for subcategory in optimized_product.subcategories.all():
            assert subcategory.category.name == 'Electronics'

    def test_annotation_and_aggregation(self, db_setup, sample_facility, sample_subcategories):
        # Create products for aggregation testing
        for i in range(5):
            product = Product.objects.create(
                name=f'Aggregation Test {i}',
                sku=f'AGG{i:03d}',
                price=decimal.Decimal(f'{100 + i * 10}.00'),
                thumbnail_url=f'https://example.com/agg{i}.jpg',
                facility=sample_facility
            )
            product.subcategories.set(sample_subcategories[:1])

        # Test aggregation queries
        from django.db.models import Count, Avg, Max, Min

        aggregated_data = Product.objects.filter(
            name__startswith='Aggregation Test'
        ).aggregate(
            total_count=Count('id'),
            avg_price=Avg('price'),
            max_price=Max('price'),
            min_price=Min('price')
        )

        assert aggregated_data['total_count'] == 5
        assert aggregated_data['avg_price'] == decimal.Decimal('120.00')
        assert aggregated_data['max_price'] == decimal.Decimal('140.00')
        assert aggregated_data['min_price'] == decimal.Decimal('100.00')

    def test_distinct_queries(self, db_setup, sample_facility, sample_subcategories):
        # Create products that share subcategories
        for i in range(3):
            product = Product.objects.create(
                name=f'Distinct Test {i}',
                sku=f'DIST{i:03d}',
                price=decimal.Decimal('75.00'),
                thumbnail_url=f'https://example.com/dist{i}.jpg',
                facility=sample_facility
            )
            product.subcategories.set(sample_subcategories)

        # Test distinct queries to avoid duplicates
        unique_products = Product.objects.filter(
            subcategories__in=sample_subcategories
        ).distinct()

        assert unique_products.count() == 3
