import pytest
import os
import tempfile
from io import BytesIO
from PIL import Image
from django.core.files.base import ContentFile
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch, Mock, MagicMock
from products.utils.image_processing import convert_to_webp, process_webp_images
from products.models import Product, Category, Subcategory, ProductImage
from products.pytest_tests.test_utils import (
    MockS3Storage, create_test_image, create_test_webp_image,
    MockImageProcessing, create_temporary_image_file, cleanup_temporary_file
)


@pytest.fixture
def sample_category():
    return Category.objects.create(
        name='Test Category',
        code='TEST001',
        thumbnail_url='https://example.com/test.jpg'
    )


@pytest.fixture
def sample_product(sample_facility):
    return Product.objects.create(
        name='Test Product',
        sku='TESTPROD001',
        price='99.99',
        thumbnail_url='https://example.com/product.jpg',
        facility=sample_facility
    )


@pytest.mark.unit
class TestImageConversion:
    def test_convert_to_webp_basic(self, db_setup):
        test_image = create_test_image(width=400, height=400)
        target_sizes = [(200, 200), (300, 300)]
        
        webp_images = convert_to_webp(test_image, target_sizes)
        
        assert len(webp_images) == 2
        
        # Verify first WebP image
        webp_images[0].seek(0)
        img1 = Image.open(webp_images[0])
        assert img1.format == 'WEBP'
        assert img1.size == (200, 200)
        
        # Verify second WebP image
        webp_images[1].seek(0)
        img2 = Image.open(webp_images[1])
        assert img2.format == 'WEBP'
        assert img2.size == (300, 300)

    def test_convert_to_webp_rgba_mode(self, db_setup):
        # Create RGBA image
        rgba_image = Image.new('RGBA', (100, 100), (255, 0, 0, 128))
        image_io = BytesIO()
        rgba_image.save(image_io, format='PNG')
        image_io.seek(0)
        
        test_file = SimpleUploadedFile('test.png', image_io.getvalue(), content_type='image/png')
        target_sizes = [(50, 50)]
        
        webp_images = convert_to_webp(test_file, target_sizes)
        
        assert len(webp_images) == 1
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.format == 'WEBP'
        assert img.mode == 'RGB'  # Should be converted to RGB

    def test_convert_to_webp_palette_mode(self, db_setup):
        # Create palette mode image
        palette_image = Image.new('P', (100, 100))
        palette_image.putpalette([i for i in range(256)] * 3)  # Simple palette
        image_io = BytesIO()
        palette_image.save(image_io, format='PNG')
        image_io.seek(0)
        
        test_file = SimpleUploadedFile('test.png', image_io.getvalue(), content_type='image/png')
        target_sizes = [(50, 50)]
        
        webp_images = convert_to_webp(test_file, target_sizes)
        
        assert len(webp_images) == 1
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.format == 'WEBP'

    def test_convert_to_webp_aspect_ratio_preservation(self, db_setup):
        # Create rectangular image
        test_image = create_test_image(width=400, height=200)  # 2:1 ratio
        target_sizes = [(100, 100)]  # Square target
        
        webp_images = convert_to_webp(test_image, target_sizes)
        
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.size == (100, 100)  # Should be cropped to square

    def test_convert_to_webp_multiple_sizes(self, db_setup):
        test_image = create_test_image(width=800, height=600)
        target_sizes = [(100, 100), (200, 200), (300, 300), (400, 400)]
        
        webp_images = convert_to_webp(test_image, target_sizes)
        
        assert len(webp_images) == 4
        
        for i, size in enumerate(target_sizes):
            webp_images[i].seek(0)
            img = Image.open(webp_images[i])
            assert img.format == 'WEBP'
            assert img.size == size

    def test_convert_to_webp_quality_optimization(self, db_setup):
        test_image = create_test_image(width=200, height=200)
        target_sizes = [(100, 100)]
        
        webp_images = convert_to_webp(test_image, target_sizes)
        
        # Verify WebP is optimized (file size should be reasonable)
        webp_size = len(webp_images[0].getvalue())
        assert webp_size > 0
        assert webp_size < 50000  # Should be compressed


@pytest.mark.unit
class TestImageProcessing:
    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_category(self, mock_storage_class, db_setup, sample_category):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        test_image = create_test_image()
        sample_category.thumbnail_image = test_image
        
        # Mock storage.open to return the test image
        mock_storage.open = Mock(return_value=test_image)
        
        target_sizes = [(200, 200), (300, 300)]
        process_webp_images(sample_category, 'Category', target_sizes)
        
        # Verify storage.save was called for both sizes
        assert mock_storage.save.call_count == 2
        
        # Verify correct file paths
        save_calls = [call[0] for call in mock_storage.save.call_args_list]
        expected_paths = [
            f'Category/{sample_category.code}_200.webp',
            f'Category/{sample_category.code}_300.webp'
        ]
        
        for expected_path in expected_paths:
            assert any(expected_path in call for call in save_calls)

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_product(self, mock_storage_class, db_setup, sample_product):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        test_image = create_test_image()
        sample_product.thumbnail_image = test_image
        
        mock_storage.open = Mock(return_value=test_image)
        
        target_sizes = [(200, 200), (300, 300)]
        process_webp_images(sample_product, 'Product', target_sizes)
        
        assert mock_storage.save.call_count == 2
        
        save_calls = [call[0] for call in mock_storage.save.call_args_list]
        expected_paths = [
            f'Product/{sample_product.sku}_200.webp',
            f'Product/{sample_product.sku}_300.webp'
        ]
        
        for expected_path in expected_paths:
            assert any(expected_path in call for call in save_calls)

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_product_image(self, mock_storage_class, db_setup, sample_product):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        product_image = ProductImage.objects.create(
            product=sample_product,
            priority=1,
            alt_text='Test image'
        )
        
        test_image = create_test_image()
        product_image.image = test_image
        
        mock_storage.open = Mock(return_value=test_image)
        
        target_sizes = [(500, 500), (600, 600), (800, 800)]
        process_webp_images(product_image, 'ProductImages', target_sizes)
        
        assert mock_storage.save.call_count == 3
        
        save_calls = [call[0] for call in mock_storage.save.call_args_list]
        expected_paths = [
            f'ProductImages/{sample_product.sku}_1_500.webp',
            f'ProductImages/{sample_product.sku}_1_600.webp',
            f'ProductImages/{sample_product.sku}_1_800.webp'
        ]
        
        for expected_path in expected_paths:
            assert any(expected_path in call for call in save_calls)

    def test_process_webp_images_no_image_field(self, db_setup, sample_category):
        # Test with instance that has no image field
        sample_category.thumbnail_image = None
        
        # Should not raise exception
        process_webp_images(sample_category, 'Category', [(200, 200)])

    def test_process_webp_images_invalid_model_type(self, db_setup, sample_category):
        test_image = create_test_image()
        sample_category.thumbnail_image = test_image
        
        with pytest.raises(ValueError):
            process_webp_images(sample_category, 'InvalidModelType', [(200, 200)])

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_missing_code_sku(self, mock_storage_class, db_setup):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        # Create category without code
        category_without_code = Category(name='No Code Category')
        test_image = create_test_image()
        category_without_code.thumbnail_image = test_image
        
        mock_storage.open = Mock(return_value=test_image)
        
        with pytest.raises(ValueError, match="No code or sku found"):
            process_webp_images(category_without_code, 'Category', [(200, 200)])

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_product_image_missing_attributes(self, mock_storage_class, db_setup):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        # Create ProductImage without product or priority
        product_image_incomplete = ProductImage()
        test_image = create_test_image()
        product_image_incomplete.image = test_image
        
        mock_storage.open = Mock(return_value=test_image)
        
        with pytest.raises(ValueError, match="ProductImage instance missing product or priority"):
            process_webp_images(product_image_incomplete, 'ProductImages', [(500, 500)])


@pytest.mark.edge_cases
class TestImageProcessingEdgeCases:
    def test_convert_to_webp_very_small_image(self, db_setup):
        # Test with 1x1 pixel image
        tiny_image = create_test_image(width=1, height=1)
        target_sizes = [(100, 100)]
        
        webp_images = convert_to_webp(tiny_image, target_sizes)
        
        assert len(webp_images) == 1
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.size == (100, 100)

    def test_convert_to_webp_very_large_image(self, db_setup):
        # Test with large image (but not too large to avoid memory issues in tests)
        large_image = create_test_image(width=2000, height=1500)
        target_sizes = [(200, 200)]
        
        webp_images = convert_to_webp(large_image, target_sizes)
        
        assert len(webp_images) == 1
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.size == (200, 200)

    def test_convert_to_webp_extreme_aspect_ratios(self, db_setup):
        # Test with very wide image
        wide_image = create_test_image(width=1000, height=100)  # 10:1 ratio
        target_sizes = [(100, 100)]
        
        webp_images = convert_to_webp(wide_image, target_sizes)
        
        webp_images[0].seek(0)
        img = Image.open(webp_images[0])
        assert img.size == (100, 100)

    def test_convert_to_webp_empty_target_sizes(self, db_setup):
        test_image = create_test_image()
        target_sizes = []
        
        webp_images = convert_to_webp(test_image, target_sizes)
        
        assert len(webp_images) == 0

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_storage_error(self, mock_storage_class, db_setup, sample_category):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        test_image = create_test_image()
        sample_category.thumbnail_image = test_image
        
        # Mock storage.open to raise exception
        mock_storage.open = Mock(side_effect=Exception('Storage error'))
        
        # Should not raise exception (error should be handled gracefully)
        try:
            process_webp_images(sample_category, 'Category', [(200, 200)])
        except Exception as e:
            # If exception is raised, it should be the storage error
            assert 'Storage error' in str(e)

    def test_convert_to_webp_corrupted_image(self, db_setup):
        # Create corrupted image data
        corrupted_data = b'not an image'
        corrupted_file = SimpleUploadedFile('corrupted.jpg', corrupted_data, content_type='image/jpeg')
        
        with pytest.raises(Exception):  # Should raise PIL exception
            convert_to_webp(corrupted_file, [(100, 100)])


@pytest.mark.performance
class TestImageProcessingPerformance:
    def test_convert_to_webp_multiple_sizes_performance(self, db_setup):
        test_image = create_test_image(width=800, height=600)
        target_sizes = [(100, 100), (200, 200), (300, 300), (400, 400), (500, 500)]
        
        # Should complete without timeout
        webp_images = convert_to_webp(test_image, target_sizes)
        
        assert len(webp_images) == 5
        
        # Verify all images are properly formatted
        for i, webp_image in enumerate(webp_images):
            webp_image.seek(0)
            img = Image.open(webp_image)
            assert img.format == 'WEBP'
            assert img.size == target_sizes[i]

    @patch('products.utils.image_processing.CategoryImageStorage')
    def test_process_webp_images_bulk_processing(self, mock_storage_class, db_setup):
        mock_storage = MockS3Storage()
        mock_storage_class.return_value = mock_storage
        
        # Create multiple categories for bulk processing
        categories = []
        for i in range(10):
            category = Category.objects.create(
                name=f'Bulk Category {i}',
                code=f'BULK{i:03d}',
                thumbnail_url=f'https://example.com/bulk{i}.jpg'
            )
            test_image = create_test_image()
            category.thumbnail_image = test_image
            categories.append(category)
        
        mock_storage.open = Mock(return_value=create_test_image())
        
        # Process all categories
        target_sizes = [(200, 200), (300, 300)]
        for category in categories:
            process_webp_images(category, 'Category', target_sizes)
        
        # Verify all were processed
        assert mock_storage.save.call_count == 20  # 10 categories * 2 sizes each
