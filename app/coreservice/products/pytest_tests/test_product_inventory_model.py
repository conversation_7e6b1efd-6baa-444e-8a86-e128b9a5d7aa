import pytest
import decimal
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from products.models import Product, ProductInventory
from products.pytest_tests.test_utils import MockTypesenseClient


@pytest.fixture
def sample_product(sample_facility):
    return Product.objects.create(
        name='Test Product for Inventory',
        sku='TESTINV001',
        price=decimal.Decimal('99.99'),
        thumbnail_url='https://example.com/test.jpg',
        facility=sample_facility
    )


@pytest.fixture
def sample_inventory_data(sample_product, sample_facility):
    return {
        'product': sample_product,
        'facility': sample_facility,
        'total_quantity': 100,
        'tolerance_quantity': 10,
        'available_quantity': 90
    }


@pytest.mark.unit
class TestProductInventoryModelBasics:
    def test_inventory_creation(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        assert inventory.id is not None
        assert inventory.product == sample_inventory_data['product']
        assert inventory.facility == sample_inventory_data['facility']
        assert inventory.total_quantity == 100
        assert inventory.tolerance_quantity == 10
        assert inventory.available_quantity == 90

    def test_inventory_str_representation(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        expected_str = f"{inventory.product.name} - {inventory.total_quantity}"
        assert str(inventory) == expected_str

    def test_inventory_default_values(self, db_setup, sample_product, sample_facility):
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility
        )
        
        assert inventory.total_quantity == 0
        assert inventory.tolerance_quantity == 0
        assert inventory.available_quantity == 0

    def test_inventory_tenant_model_inheritance(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        # Should inherit TenantModel fields
        assert hasattr(inventory, 'creation_date')
        assert hasattr(inventory, 'updation_date')
        assert hasattr(inventory, 'facility')
        assert inventory.creation_date is not None
        assert inventory.updation_date is not None


@pytest.mark.validation
class TestProductInventoryValidation:
    def test_negative_quantities_allowed(self, db_setup, sample_product, sample_facility):
        # Test that negative quantities are allowed (business requirement)
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility,
            total_quantity=-10,
            tolerance_quantity=-5,
            available_quantity=-15
        )
        
        assert inventory.total_quantity == -10
        assert inventory.tolerance_quantity == -5
        assert inventory.available_quantity == -15

    def test_zero_quantities(self, db_setup, sample_product, sample_facility):
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility,
            total_quantity=0,
            tolerance_quantity=0,
            available_quantity=0
        )
        
        assert inventory.total_quantity == 0
        assert inventory.tolerance_quantity == 0
        assert inventory.available_quantity == 0

    def test_large_quantities(self, db_setup, sample_product, sample_facility):
        large_quantity = 2147483647  # Max integer value
        
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility,
            total_quantity=large_quantity,
            tolerance_quantity=large_quantity,
            available_quantity=large_quantity
        )
        
        assert inventory.total_quantity == large_quantity
        assert inventory.tolerance_quantity == large_quantity
        assert inventory.available_quantity == large_quantity

    def test_inventory_field_types(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        assert isinstance(inventory.total_quantity, int)
        assert isinstance(inventory.tolerance_quantity, int)
        assert isinstance(inventory.available_quantity, int)


@pytest.mark.relationships
class TestProductInventoryRelationships:
    def test_product_inventory_relationship(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        assert inventory.product == sample_inventory_data['product']
        assert inventory in inventory.product.inventory.all()

    def test_facility_inventory_relationship(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        assert inventory.facility == sample_inventory_data['facility']
        # Note: Facility doesn't have reverse relationship defined in the model

    def test_cascade_deletion_from_product(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        product = inventory.product
        inventory_id = inventory.id
        
        product.delete()
        
        assert not ProductInventory.objects.filter(id=inventory_id).exists()

    def test_multiple_inventories_per_product(self, db_setup, sample_product):
        from authz.models.tenant import Facility
        
        facility1 = sample_product.facility
        facility2 = Facility.objects.create(name='Second Facility')
        
        inventory1 = ProductInventory.objects.create(
            product=sample_product,
            facility=facility1,
            total_quantity=100
        )
        inventory2 = ProductInventory.objects.create(
            product=sample_product,
            facility=facility2,
            total_quantity=200
        )
        
        assert sample_product.inventory.count() == 2
        assert inventory1 in sample_product.inventory.all()
        assert inventory2 in sample_product.inventory.all()

    def test_inventory_filtering_by_facility(self, db_setup, sample_product):
        from authz.models.tenant import Facility
        
        facility1 = sample_product.facility
        facility2 = Facility.objects.create(name='Second Facility')
        
        inventory1 = ProductInventory.objects.create(
            product=sample_product,
            facility=facility1,
            total_quantity=100
        )
        inventory2 = ProductInventory.objects.create(
            product=sample_product,
            facility=facility2,
            total_quantity=200
        )
        
        facility1_inventories = ProductInventory.objects.filter(facility=facility1)
        facility2_inventories = ProductInventory.objects.filter(facility=facility2)
        
        assert inventory1 in facility1_inventories
        assert inventory1 not in facility2_inventories
        assert inventory2 in facility2_inventories
        assert inventory2 not in facility1_inventories


@pytest.mark.business_logic
class TestProductInventoryBusinessLogic:
    def test_inventory_quantity_calculations(self, db_setup, sample_product, sample_facility):
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility,
            total_quantity=100,
            tolerance_quantity=10,
            available_quantity=85
        )
        
        # Test basic quantity relationships
        assert inventory.total_quantity >= inventory.available_quantity
        assert inventory.tolerance_quantity <= inventory.total_quantity
        
        # Test quantity updates
        inventory.total_quantity = 150
        inventory.available_quantity = 130
        inventory.save()
        
        updated_inventory = ProductInventory.objects.get(id=inventory.id)
        assert updated_inventory.total_quantity == 150
        assert updated_inventory.available_quantity == 130

    def test_inventory_stock_scenarios(self, db_setup, sample_product, sample_facility):
        # Test various stock scenarios
        scenarios = [
            {'total': 100, 'tolerance': 10, 'available': 90, 'scenario': 'normal_stock'},
            {'total': 5, 'tolerance': 10, 'available': 0, 'scenario': 'low_stock'},
            {'total': 0, 'tolerance': 5, 'available': 0, 'scenario': 'out_of_stock'},
            {'total': 1000, 'tolerance': 50, 'available': 950, 'scenario': 'high_stock'},
        ]
        
        for i, scenario in enumerate(scenarios):
            inventory = ProductInventory.objects.create(
                product=sample_product,
                facility=sample_facility,
                total_quantity=scenario['total'],
                tolerance_quantity=scenario['tolerance'],
                available_quantity=scenario['available']
            )
            
            # Verify scenario data
            assert inventory.total_quantity == scenario['total']
            assert inventory.tolerance_quantity == scenario['tolerance']
            assert inventory.available_quantity == scenario['available']
            
            # Clean up for next iteration
            inventory.delete()

    def test_inventory_quantity_adjustments(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        original_total = inventory.total_quantity
        original_available = inventory.available_quantity
        
        # Simulate stock addition
        inventory.total_quantity += 50
        inventory.available_quantity += 45  # 5 reserved
        inventory.save()
        
        assert inventory.total_quantity == original_total + 50
        assert inventory.available_quantity == original_available + 45
        
        # Simulate stock reduction
        inventory.total_quantity -= 30
        inventory.available_quantity -= 25  # 5 additional reserved
        inventory.save()
        
        assert inventory.total_quantity == original_total + 20
        assert inventory.available_quantity == original_available + 20


@pytest.mark.edge_cases
class TestProductInventoryEdgeCases:
    def test_inventory_with_same_product_different_facilities(self, db_setup, sample_product):
        from authz.models.tenant import Facility
        
        facilities = []
        inventories = []
        
        for i in range(3):
            facility = Facility.objects.create(name=f'Facility {i}')
            facilities.append(facility)
            
            inventory = ProductInventory.objects.create(
                product=sample_product,
                facility=facility,
                total_quantity=100 * (i + 1),
                available_quantity=90 * (i + 1)
            )
            inventories.append(inventory)
        
        # Verify all inventories exist for the same product
        assert sample_product.inventory.count() == 3
        
        # Verify each facility has correct inventory
        for i, facility in enumerate(facilities):
            facility_inventory = ProductInventory.objects.get(
                product=sample_product,
                facility=facility
            )
            assert facility_inventory.total_quantity == 100 * (i + 1)

    def test_inventory_extreme_values(self, db_setup, sample_product, sample_facility):
        # Test with extreme integer values
        max_int = 2147483647
        min_int = -2147483648
        
        inventory = ProductInventory.objects.create(
            product=sample_product,
            facility=sample_facility,
            total_quantity=max_int,
            tolerance_quantity=min_int,
            available_quantity=0
        )
        
        assert inventory.total_quantity == max_int
        assert inventory.tolerance_quantity == min_int
        assert inventory.available_quantity == 0

    def test_inventory_concurrent_updates(self, db_setup, sample_inventory_data):
        inventory = ProductInventory.objects.create(**sample_inventory_data)
        
        # Simulate concurrent updates (basic test)
        inventory1 = ProductInventory.objects.get(id=inventory.id)
        inventory2 = ProductInventory.objects.get(id=inventory.id)
        
        inventory1.total_quantity = 150
        inventory1.save()
        
        inventory2.available_quantity = 140
        inventory2.save()
        
        # Last save wins
        final_inventory = ProductInventory.objects.get(id=inventory.id)
        assert final_inventory.total_quantity == 100  # Original value
        assert final_inventory.available_quantity == 140  # Updated value


@pytest.mark.performance
class TestProductInventoryPerformance:
    def test_bulk_inventory_creation(self, db_setup, sample_facility):
        # Create multiple products
        products = []
        for i in range(50):
            product = Product.objects.create(
                name=f'Bulk Product {i}',
                sku=f'BULK{i:03d}',
                price=decimal.Decimal('10.00'),
                thumbnail_url=f'https://example.com/bulk{i}.jpg',
                facility=sample_facility
            )
            products.append(product)
        
        # Create inventories in bulk
        inventories_data = []
        for product in products:
            inventories_data.append(ProductInventory(
                product=product,
                facility=sample_facility,
                total_quantity=100,
                tolerance_quantity=10,
                available_quantity=90
            ))
        
        created_inventories = ProductInventory.objects.bulk_create(inventories_data)
        assert len(created_inventories) == 50
        assert ProductInventory.objects.count() >= 50

    def test_efficient_inventory_queries(self, db_setup, sample_facility):
        from authz.models.tenant import Facility
        
        # Create additional facilities
        facilities = [sample_facility]
        for i in range(2):
            facility = Facility.objects.create(name=f'Query Facility {i}')
            facilities.append(facility)
        
        # Create products and inventories
        for i in range(10):
            product = Product.objects.create(
                name=f'Query Product {i}',
                sku=f'QP{i:03d}',
                price=decimal.Decimal('25.00'),
                thumbnail_url=f'https://example.com/qp{i}.jpg',
                facility=sample_facility
            )
            
            # Create inventory for each facility
            for facility in facilities:
                ProductInventory.objects.create(
                    product=product,
                    facility=facility,
                    total_quantity=100 + i * 10,
                    available_quantity=90 + i * 10
                )
        
        # Test efficient querying with select_related
        inventories_with_products = ProductInventory.objects.select_related(
            'product', 'facility'
        ).filter(product__name__startswith='Query Product')
        
        assert inventories_with_products.count() == 30  # 10 products * 3 facilities
        
        # Verify relationships are properly loaded
        for inventory in inventories_with_products[:5]:
            assert inventory.product.name.startswith('Query Product')
            assert inventory.facility.name in ['Test Facility', 'Query Facility 0', 'Query Facility 1']
