import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from products.models import Category, Subcategory
from django.db import transaction
from products.pytest_tests.test_utils import (
    assert_typesense_document_structure,
    validate_subcategory_constraints
)
from unittest.mock import patch
from django.core.files.uploadedfile import SimpleUploadedFile


@pytest.mark.unit
class TestSubcategoryModelBasics:
    def test_subcategory_creation(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )
        
        assert subcategory.id is not None
        assert subcategory.name == sample_subcategory_data['name']
        assert subcategory.code == sample_subcategory_data['code']
        assert subcategory.category == category
        assert subcategory.is_active == sample_subcategory_data['is_active']
        validate_subcategory_constraints(subcategory)
            
        expected_str = f"{category.name} - {subcategory.name}"
        assert str(subcategory) == expected_str
        
        assert subcategory._meta.db_table == 'product_subcategories'
        assert subcategory._meta.ordering == ['category__name', 'name']
        assert subcategory._meta.verbose_name_plural == 'Subcategories'


@pytest.mark.unit
class TestSubcategoryValidation:
    def test_unique_code_constraint(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )
        duplicate_data = sample_subcategory_data.copy()
        duplicate_data['name'] = 'Different Name'
        
        with pytest.raises(IntegrityError):
            Subcategory.objects.create(
                **duplicate_data,
                category=category
            )
    
    def test_required_fields(self, db_setup, sample_category_data):
        category = Category.objects.create(**sample_category_data)

        subcategory_no_name = Subcategory.objects.create(
            code="SUB_NO_NAME_001",
            category=category,
            thumbnail_url="https://example.com/test.jpg"
        )
        assert subcategory_no_name.name == ""  # Empty string, not None

        existing_empty_code = Subcategory.objects.filter(code="").exists()
        if not existing_empty_code:
            subcategory_no_code = Subcategory.objects.create(
                name="Test Subcategory",
                category=category,
                thumbnail_url="https://example.com/test.jpg"
            )
            assert subcategory_no_code.code == ""  # Empty string allowed

            with transaction.atomic():
                with pytest.raises(IntegrityError):
                    Subcategory.objects.create(
                        name="Test Subcategory 2",
                        category=category,
                        thumbnail_url="https://example.com/test2.jpg"
                    )

        with transaction.atomic():
            with pytest.raises(IntegrityError):
                Subcategory.objects.create(
                    name="Test Subcategory",
                    code="SUB_NO_CAT_002",
                    thumbnail_url="https://example.com/test.jpg"
                )

        subcategory_no_url = Subcategory.objects.create(
            name="Test Subcategory",
            code="SUB_NO_URL_003",
            category=category
        )
        assert subcategory_no_url.thumbnail_url == ""  # Empty string allowed
    
    def test_field_length_constraints(self, db_setup, sample_category_data):
        """Test field length constraints"""
        category = Category.objects.create(**sample_category_data)
        
        long_name = "x" * 101
        with pytest.raises(Exception):
            Subcategory.objects.create(
                name=long_name,
                code="SUB_LONG_NAME_001",
                category=category,
                thumbnail_url="https://example.com/test.jpg"
            )

        long_code = "x" * 51
        with pytest.raises(Exception):
            Subcategory.objects.create(
                name="Test Subcategory",
                code=long_code,
                category=category,
                thumbnail_url="https://example.com/test.jpg"
            )

        long_url = "https://example.com/" + "x" * 500
        with pytest.raises(Exception):
            Subcategory.objects.create(
                name="Test Subcategory",
                code="SUB_LONG_URL_001",
                category=category,
                thumbnail_url=long_url
            )


@pytest.mark.unit
class TestSubcategoryTypesenseIntegration:
    def test_to_typesense_dict_structure(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )
        
        typesense_dict = subcategory.to_typesense_dict()
        
        expected_fields = ['id', 'name', 'description', 'code', 'category_id', 'category_name', 'is_active', 'thumbnail_url']
        assert_typesense_document_structure(typesense_dict, expected_fields)
        
        assert typesense_dict['id'] == str(subcategory.id)
        assert typesense_dict['name'] == subcategory.name
        assert typesense_dict['description'] == subcategory.description
        assert typesense_dict['code'] == subcategory.code
        assert typesense_dict['category_id'] == category.id
        assert typesense_dict['category_name'] == category.name
        assert typesense_dict['is_active'] == subcategory.is_active
        assert typesense_dict['thumbnail_url'] == subcategory.thumbnail_url


@pytest.mark.unit
class TestSubcategoryOrdering:
    
    def test_subcategory_ordering(self, db_setup):
        Subcategory.objects.all().delete()
        Category.objects.all().delete()

        category_a = Category.objects.create(
            name="A Category",
            code="CAT_ORDER_A",
            thumbnail_url="https://example.com/a.jpg"
        )
        category_b = Category.objects.create(
            name="B Category",
            code="CAT_ORDER_B",
            thumbnail_url="https://example.com/b.jpg"
        )

        Subcategory.objects.create(
            name="Z Subcategory",
            code="SUB_ORDER_Z",
            category=category_b,
            thumbnail_url="https://example.com/z.jpg"
        )
        Subcategory.objects.create(
            name="A Subcategory",
            code="SUB_ORDER_A",
            category=category_a,
            thumbnail_url="https://example.com/a.jpg"
        )
        Subcategory.objects.create(
            name="B Subcategory",
            code="SUB_ORDER_B",
            category=category_a,
            thumbnail_url="https://example.com/b.jpg"
        )

        subcategories = Subcategory.objects.select_related('category').order_by('category__name', 'name')

        expected_order = [
            ("A Category", "A Subcategory"),
            ("A Category", "B Subcategory"),
            ("B Category", "Z Subcategory")
        ]

        actual_order = [(sub.category.name, sub.name) for sub in subcategories]
        assert actual_order == expected_order


@pytest.mark.unit
class TestSubcategoryEdgeCases:
    
    def test_subcategory_with_same_name_different_categories(self, db_setup):
        category1 = Category.objects.create(
            name="Category 1",
            code="CAT001",
            thumbnail_url="https://example.com/cat1.jpg"
        )
        category2 = Category.objects.create(
            name="Category 2", 
            code="CAT002",
            thumbnail_url="https://example.com/cat2.jpg"
        )
        
        subcategory1 = Subcategory.objects.create(
            name="Common Name",
            code="SUB_COMMON_001",
            category=category1,
            thumbnail_url="https://example.com/sub1.jpg"
        )
        subcategory2 = Subcategory.objects.create(
            name="Common Name",
            code="SUB_COMMON_002",  # Different code required
            category=category2,
            thumbnail_url="https://example.com/sub2.jpg"
        )
        
        assert subcategory1.name == subcategory2.name
        assert subcategory1.category != subcategory2.category
        assert subcategory1.code != subcategory2.code


@pytest.mark.signals
class TestSubcategorySignals:
    def test_typesense_integration_on_create(self, db_setup, sample_category_data, sample_subcategory_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )

        documents = mock_client.collections['subcategories'].documents
        assert len(documents.upsert_calls) == 1

        upserted_doc = documents.upsert_calls[0]
        assert upserted_doc['id'] == str(subcategory.id)
        assert upserted_doc['name'] == subcategory.name
        assert upserted_doc['code'] == subcategory.code
        assert upserted_doc['category_id'] == category.id
        assert upserted_doc['category_name'] == category.name

    def test_typesense_integration_on_update(self, db_setup, sample_category_data, sample_subcategory_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )

        mock_client.reset()

        subcategory.name = "Updated Subcategory Name"
        subcategory.save()

        documents = mock_client.collections['subcategories'].documents
        assert len(documents.upsert_calls) >= 1

        upserted_doc = documents.upsert_calls[0]
        assert upserted_doc['name'] == "Updated Subcategory Name"

    def test_typesense_integration_on_delete(self, db_setup, sample_category_data, sample_subcategory_data, typesense_mock):
        mock_client = typesense_mock

        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )
        subcategory_id = str(subcategory.id)

        documents = mock_client.collections['subcategories'].documents
        documents.delete_calls.clear()

        subcategory.delete()


@pytest.mark.signals
class TestSubcategoryImageProcessingSignals:
    def test_image_processing_signal_on_create_with_image(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)

        image_content = b"fake image content"
        uploaded_file = SimpleUploadedFile(
            "test_subcategory.jpg",
            image_content,
            content_type="image/jpeg"
        )

        subcategory_data = sample_subcategory_data.copy()
        subcategory_data['code'] = 'SUB_IMG_CREATE_001'

        with patch('products.models.category.process_webp_images') as mock_process:
            subcategory = Subcategory.objects.create(
                **subcategory_data,
                category=category,
                thumbnail_image=uploaded_file
            )

            mock_process.assert_called_once_with(
                subcategory, 'Subcategory', [(200, 200), (300, 300)]
            )

            if subcategory.thumbnail_image:
                try:
                    subcategory.thumbnail_image.delete(save=False)
                except:
                    pass

    def test_image_processing_signal_on_update_with_image(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory_data = sample_subcategory_data.copy()
        subcategory_data['code'] = 'SUB_IMG_UPDATE_001'

        subcategory = Subcategory.objects.create(
            **subcategory_data,
            category=category
        )

        with patch('products.models.category.process_webp_images') as mock_process:
            image_content = b"fake updated image content"
            uploaded_file = SimpleUploadedFile(
                "updated_subcategory.jpg",
                image_content,
                content_type="image/jpeg"
            )
            subcategory.thumbnail_image = uploaded_file
            subcategory.save()

            mock_process.assert_called_once_with(
                subcategory, 'Subcategory', [(200, 200), (300, 300)]
            )

            if subcategory.thumbnail_image:
                try:
                    subcategory.thumbnail_image.delete(save=False)
                except:
                    pass

    def test_no_image_processing_without_image(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory_data = sample_subcategory_data.copy()
        subcategory_data['code'] = 'SUB_NO_IMG_001'

        with patch('products.models.category.process_webp_images') as mock_process:
            Subcategory.objects.create(
                **subcategory_data,
                category=category
            )

            mock_process.assert_not_called()


@pytest.mark.unit
class TestSubcategoryCascadeDeletion:
    def test_subcategory_deleted_when_category_deleted(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory = Subcategory.objects.create(
            **sample_subcategory_data,
            category=category
        )

        subcategory_id = subcategory.id
        assert Subcategory.objects.filter(id=subcategory_id).exists()

        category.delete()

        assert not Subcategory.objects.filter(id=subcategory_id).exists()

    def test_multiple_subcategories_deleted_with_category(self, db_setup, sample_category_data):
        category = Category.objects.create(**sample_category_data)

        subcategory1 = Subcategory.objects.create(
            name="Subcategory 1",
            code="SUB_CASCADE_001",
            category=category,
            thumbnail_url="https://example.com/sub1.jpg"
        )
        subcategory2 = Subcategory.objects.create(
            name="Subcategory 2",
            code="SUB_CASCADE_002",
            category=category,
            thumbnail_url="https://example.com/sub2.jpg"
        )

        assert Subcategory.objects.filter(category=category).count() == 2

        category.delete()

        assert not Subcategory.objects.filter(id__in=[subcategory1.id, subcategory2.id]).exists()


@pytest.mark.unit
class TestSubcategoryErrorHandling:
    def test_signal_error_handling(self, db_setup, sample_category_data, sample_subcategory_data):
        category = Category.objects.create(**sample_category_data)
        subcategory_data = sample_subcategory_data.copy()
        subcategory_data['code'] = 'SUB_SIGNAL_ERROR_001'

        with patch('products.models.category._upsert_to_typesense') as mock_upsert:
            mock_upsert.side_effect = Exception("Typesense error")

            try:
                subcategory = Subcategory.objects.create(
                    **subcategory_data,
                    category=category
                )
                assert subcategory.id is not None
                assert Subcategory.objects.filter(id=subcategory.id).exists()
            except Exception as e:
                assert "Typesense error" in str(e)

    def test_duplicate_code_different_categories(self, db_setup):
        category1 = Category.objects.create(
            name="Category 1",
            code="CAT001",
            thumbnail_url="https://example.com/cat1.jpg"
        )
        category2 = Category.objects.create(
            name="Category 2",
            code="CAT002",
            thumbnail_url="https://example.com/cat2.jpg"
        )

        Subcategory.objects.create(
            name="Subcategory 1",
            code="SUB_DUPLICATE_TEST",
            category=category1,
            thumbnail_url="https://example.com/sub1.jpg"
        )
        with pytest.raises(IntegrityError):
            Subcategory.objects.create(
                name="Subcategory 2",
                code="SUB_DUPLICATE_TEST",  # Same code
                category=category2,
                thumbnail_url="https://example.com/sub2.jpg"
            )

