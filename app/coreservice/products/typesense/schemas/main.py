products_schema = {
        'name': 'products',
         'fields': [
                {'name': 'id', 'type': 'string'},
                {'name': 'name', 'type': 'string'},
                {'name': 'description', 'type': 'string'},
                {'name': 'sku', 'type': 'string'},
                {'name': 'price', 'type': 'float'},
                {'name': 'brand', 'type': 'string', 'optional': True, 'facet': True},
                {'name': 'is_active', 'type': 'bool'},
                {'name': 'facility_id', 'type': 'string', 'facet': True},
                {'name': 'facility_name', 'type': 'string', 'facet': True},
                {'name': 'categories', 'type': 'string[]', 'facet': True},
                {'name': 'subcategories', 'type': 'string[]', 'facet': True},
                {'name': 'created_at', 'type': 'string'},
                {'name': 'updated_at', 'type': 'string'},
                {'name': 'weight', 'type': 'float', 'optional': True},
                {'name': 'weight_unit', 'type': 'string', 'optional': True},
                {'name': 'image_url', 'type': 'string[]', 'optional': True},
                {'name': 'thumbnail_url', 'type': 'string', 'optional': True},
                {'name': 'display_alias', 'type': 'string[]', 'optional': True},
                {'name': 'tags', 'type': 'string[]', 'optional': True, 'facet': True},
                {'name': 'colour', 'type': 'string', 'optional': True},
            ],
            "token_separators": ['-', ' ']
    }

categories_schema = {
        'name': 'categories',
        'fields': [
            {'name': 'id', 'type': 'string'},
            {'name': 'name', 'type': 'string'},
            {'name': 'description', 'type': 'string', 'optional': True},
            {'name': 'code', 'type': 'string'},
            {'name': 'is_active', 'type': 'bool'},
            {'name': 'thumbnail_url', 'type': 'string'},
            {'name': 'section', 'type': 'string', 'facet': True},
        ],
    }

subcategories_schema = {
        'name': 'subcategories',
        'fields': [
            {'name': 'id', 'type': 'string'},
            {'name': 'name', 'type': 'string'},
            {'name': 'description', 'type': 'string', 'optional': True},
            {'name': 'code', 'type': 'string'},
            {'name': 'category_id', 'type': 'int32', 'facet': True},
            {'name': 'category_name', 'type': 'string', 'facet': True},
            {'name': 'is_active', 'type': 'bool'},
            {'name': 'thumbnail_url', 'type': 'string'},
        ],
}

