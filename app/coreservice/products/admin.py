from django.contrib import admin
from products.models import Product, Category, Subcategory, ProductImage


@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active', 'has_thumbnail_image')
    list_filter = ('is_active', 'section')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'code', 'section')
        }),
        ('Images', {
            'fields': ('thumbnail_image', 'thumbnail_url'),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_thumbnail_image(self, obj):
        return bool(obj.thumbnail_image)
    has_thumbnail_image.boolean = True
    has_thumbnail_image.short_description = 'Has Image'


@admin.register(Subcategory)
class SubcategoryAdmin(admin.ModelAdmin):
    list_display = ('name', 'category', 'code', 'is_active', 'has_thumbnail_image')
    list_filter = ('category', 'is_active')
    search_fields = ('name', 'code', 'description')
    readonly_fields = ('creation_date', 'updation_date')

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'code', 'category')
        }),
        ('Images', {
            'fields': ('thumbnail_image', 'thumbnail_url'),
            'description': 'Upload an image file (stored with original filename, WebP versions generated automatically) OR provide a direct URL.'
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_thumbnail_image(self, obj):
        return bool(obj.thumbnail_image)
    has_thumbnail_image.boolean = True
    has_thumbnail_image.short_description = 'Has Image'


class ProductImageInline(admin.TabularInline):
    """
    Inline admin for ProductImage model to allow multiple image uploads per product
    """
    model = ProductImage
    extra = 1
    max_num = 10
    fields = ('image', 'priority', 'alt_text', 'is_primary')


@admin.register(ProductImage)
class ProductImageAdmin(admin.ModelAdmin):
    """
    Standalone admin for ProductImage model (optional, for advanced management)
    """
    list_display = ('product', 'priority', 'is_primary', 'alt_text')
    list_filter = ('is_primary', 'product__brand')
    search_fields = ('product__name', 'product__sku', 'alt_text')


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = ('name', 'sku', 'price', 'brand', 'facility', 'is_active', 'has_thumbnail_image', 'image_count')
    list_filter = ('is_active', 'facility', 'brand', 'subcategories')
    search_fields = ('name', 'sku', 'description')
    readonly_fields = ('creation_date', 'updation_date')
    filter_horizontal = ('subcategories',)
    inlines = [ProductImageInline]

    fieldsets = (
        (None, {
            'fields': ('name', 'description', 'sku', 'price')
        }),
        ('Categorization', {
            'fields': ('subcategories', 'brand', 'colour')
        }),
        ('Facility', {
            'fields': ('facility',)
        }),
        ('Product Details', {
            'fields': ('weight', 'weight_unit', 'display_alias', 'tags', 'attributes')
        }),
        ('Images', {
            'fields': ('thumbnail_image', 'thumbnail_url', 'image_url'),
            'description': 'Upload a thumbnail image (converted to WebP) OR provide direct URLs. All fields are independent.',
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('creation_date', 'updation_date'),
            'classes': ('collapse',)
        }),
    )

    def has_thumbnail_image(self, obj):
        return bool(obj.thumbnail_image)
    has_thumbnail_image.boolean = True
    has_thumbnail_image.short_description = 'Has Thumbnail'

    def image_count(self, obj):
        return obj.product_images.count()
    image_count.short_description = "Images"


