from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from products.models import Product, Category, Subcategory
from products.serializers import ProductSerializer, ProductSearchSerializer, CategorySerializer, SubcategorySerializer
from products.typesense.client import get_typesense_client


class CategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product categories
    """
    queryset = Category.objects.all()
    serializer_class = CategorySerializer


class SubcategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for product subcategories
    """
    queryset = Subcategory.objects.all()
    serializer_class = SubcategorySerializer
    
    def get_queryset(self):
        queryset = Subcategory.objects.all()
        category_id = self.request.query_params.get('category_id', None)
        if category_id is not None:
            queryset = queryset.filter(category_id=category_id)
        return queryset


class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for products
    """
    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    
    def get_queryset(self):
        queryset = Product.objects.all()
        facility_id = self.request.query_params.get('facility_id', None)
        if facility_id is not None:
            queryset = queryset.filter(facility_id=facility_id)
        return queryset

    @action(detail=False, methods=['post'])
    def search(self, request):
        """
        Search products using Typesense
        """
        serializer = ProductSearchSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        validated_data = serializer.validated_data
        query = validated_data.get('query', '')
        category = validated_data.get('category', '')
        subcategory = validated_data.get('subcategory', '')
        facility_id = validated_data.get('facility_id', '')
        brand = validated_data.get('brand', '')
        min_price = validated_data.get('min_price')
        max_price = validated_data.get('max_price')
        sort_by = validated_data.get('sort_by', 'name')
        sort_order = validated_data.get('sort_order', 'asc')
        
        # Build search parameters
        search_params = {
            'q': query,
            'query_by': 'name,description,sku',
            'sort_by': f'{sort_by}:{sort_order}',
            'per_page': 100,
            'filter_by': '',
            'facet_by': 'categories,subcategories,brand,facility_name'
        }
        
        # Add filters if provided
        filters = []
        if category:
            filters.append(f'categories:=[{category}]')
        if subcategory:
            filters.append(f'subcategories:=[{subcategory}]')
        if facility_id:
            filters.append(f'facility_id:={facility_id}')
        if brand:
            filters.append(f'brand:={brand}')
        if min_price is not None:
            filters.append(f'price:>={min_price}')
        if max_price is not None:
            filters.append(f'price:<={max_price}')
        
        if filters:
            search_params['filter_by'] = ' && '.join(filters)
        
        try:
            client = get_typesense_client()
            search_results = client.collections['products'].documents.search(search_params)
            return Response(search_results)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
