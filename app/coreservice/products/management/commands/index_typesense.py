from django.core.management.base import BaseCommand
import requests
import time
from products.models.product import Product
from products.models.category import Category
from products.models.category import Subcategory
from products.typesense.client import get_typesense_client, initialize_typesense_schema, check_typesense_health
import os


class Command(BaseCommand):
    help = 'Index all products, categories, and subcategories in Typesense'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force reindexing of all products, categories, and subcategories even if they are already indexed',
        )

    def handle(self, *args, **options):
        force = options.get('force', False)
        
        self.stdout.write("Starting indexing of products, categories, and subcategories...")
        
        # First check if Typesense is healthy
        if not check_typesense_health(self):
            self.stdout.write("Waiting for Typesense to become healthy...")
            for _ in range(5):  # Try 5 times with 2 second intervals
                time.sleep(2)
                if self.check_typesense_health():
                    break
            else:
                self.stdout.write(self.style.ERROR("Typesense is not healthy. Exiting."))
                return
        
        # Initialize schema
        try:
            initialize_typesense_schema()
            # self.stdout.write(self.style.SUCCESS("✅ Schema initialized successfully"))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"❌ Error initializing schema: {e}"))
            return
        
        # Get Typesense client
        client = get_typesense_client()
        
        # Index Categories
        self.index_categories(client, force)
        
        # Index Subcategories
        self.index_subcategories(client, force)

        # Index Products
        self.index_products(client, force)
        
        self.stdout.write(self.style.SUCCESS("\nIndexing complete."))

    def index_products(self, client, force):
        self.stdout.write("\nStarting product indexing...")
        products = Product.objects.all()
        self.stdout.write(f"Found {products.count()} products to index")
        
        success_count = 0
        error_count = 0
        
        for product in products:
            try:
                document = product.to_typesense_dict()
                client.collections['products'].documents.upsert(document)
                success_count += 1
                # self.stdout.write(f"✅ Indexed product {product.id}: {product.name}")
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f"❌ Error indexing product {product.id}: {e}"))
        
        self.stdout.write(self.style.SUCCESS(
            f"Product indexing complete: {success_count} products indexed successfully, {error_count} errors"
        ))

    def index_categories(self, client, force):
        self.stdout.write("\nStarting category indexing...")
        categories = Category.objects.all()
        self.stdout.write(f"Found {categories.count()} categories to index")
        
        success_count = 0
        error_count = 0
        
        for category in categories:
            try:
                document = category.to_typesense_dict()
                client.collections['categories'].documents.upsert(document)
                success_count += 1
                # self.stdout.write(f"✅ Indexed category {category.id}: {category.name}")
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f"❌ Error indexing category {category.id}: {e}"))
        
        self.stdout.write(self.style.SUCCESS(
            f"Category indexing complete: {success_count} categories indexed successfully, {error_count} errors"
        ))

    def index_subcategories(self, client, force):
        self.stdout.write("\nStarting subcategory indexing...")
        subcategories = Subcategory.objects.all()
        self.stdout.write(f"Found {subcategories.count()} subcategories to index")
        
        success_count = 0
        error_count = 0
        
        for subcategory in subcategories:
            try:
                document = subcategory.to_typesense_dict()
                client.collections['subcategories'].documents.upsert(document)
                success_count += 1
                # self.stdout.write(f"✅ Indexed subcategory {subcategory.id}: {subcategory.name}")
            except Exception as e:
                error_count += 1
                self.stdout.write(self.style.ERROR(f"❌ Error indexing subcategory {subcategory.id}: {e}"))
        
        self.stdout.write(self.style.SUCCESS(
            f"Subcategory indexing complete: {success_count} subcategories indexed successfully, {error_count} errors"
        ))
