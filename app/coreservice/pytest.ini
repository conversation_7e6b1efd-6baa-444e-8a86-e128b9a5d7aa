[pytest]
DJANGO_SETTINGS_MODULE = coreservice.settings
python_files = tests.py test_*.py *_tests.py
addopts =
    --tb=short
    --strict-markers
    --reuse-db
    --nomigrations
    -v
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    unit: marks tests as unit tests
    signals: marks tests that test Django signals
    typesense: marks tests that involve Typesense integration
    image_processing: marks tests that involve image processing
    s3: marks tests that involve S3 storage
    performance: marks tests that test performance characteristics
    database: marks tests that test database operations
testpaths = products/pytest_tests authz/pytest_tests orders/pytest_tests
filterwarnings =
    ignore::django.utils.deprecation.RemovedInDjango61Warning
    ignore::DeprecationWarning
    ignore::UserWarning
    ignore::PendingDeprecationWarning