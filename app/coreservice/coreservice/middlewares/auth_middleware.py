from django.http import JsonResponse
from rest_framework import status
from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON>entication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from django.urls import resolve
import re


class JWTAuthenticationMiddleware:
    """
    Custom middleware for JWT authentication.
    This middleware will validate JWT tokens for all protected routes
    and return appropriate error responses if the token is invalid.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.jwt_authenticator = JWTAuthentication()
        # Define paths that don't require authentication
        self.public_paths = [
            r'^/admin/',
            r'^/api/auth/login/',
            r'^/api/auth/token/refresh/',
            # Add more public paths as needed
        ]
    
    def __call__(self, request):
        # Check if the current path is in the public paths list
        path = request.path_info
        
        # Skip authentication for public paths
        if any(re.match(pattern, path) for pattern in self.public_paths):
            return self.get_response(request)
        
        # Try to authenticate the request
        try:
            # Get the authorization header
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            
            # If no auth header is present, return 401
            if not auth_header or not auth_header.startswith('Bearer '):
                return JsonResponse(
                    {'detail': 'Authentication credentials were not provided.'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Authenticate the request
            auth_result = self.jwt_authenticator.authenticate(request)
            
            # If authentication fails, return 401
            if auth_result is None:
                return JsonResponse(
                    {'detail': 'Invalid token.'},
                    status=status.HTTP_401_UNAUTHORIZED
                )
            
            # Unpack the authentication result
            user, token = auth_result
            
            # Set the authenticated user on the request
            request.user = user
            
            # Continue processing the request
            return self.get_response(request)
            
        except InvalidToken:
            return JsonResponse(
                {'detail': 'Token is invalid or expired.'},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except TokenError as e:
            return JsonResponse(
                {'detail': str(e)},
                status=status.HTTP_401_UNAUTHORIZED
            )
        except Exception as e:
            return JsonResponse(
                {'detail': 'Authentication failed.'},
                status=status.HTTP_401_UNAUTHORIZED
            )
