import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from django.contrib.auth.models import Permission
from authz.models import CompanyGroup, CompanyMaster, User


@pytest.mark.unit
class TestCompanyGroupModelBasics:
    def test_company_group_creation(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Test Group',
            company=sample_company,
            description='Test group description',
            code_name='test_group'
        )
        
        assert group.id is not None
        assert group.name == 'Test Group'
        assert group.company == sample_company
        assert group.description == 'Test group description'
        assert group.code_name == 'test_group'
        assert group.creation_date is not None
        assert group.updation_date is not None
        assert str(group) == 'Test Group'

    def test_company_group_default_values(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Default Group',
            company=sample_company
        )
        
        assert group.description == ''
        assert group.code_name == ''
        assert group.creation_date is not None
        assert group.updation_date is not None

    def test_company_group_without_company(self, db_setup):
        group = CompanyGroup.objects.create(
            name='No Company Group',
            description='Group without company',
            code_name='no_company'
        )
        
        assert group.company is None
        assert group.name == 'No Company Group'
        assert group.description == 'Group without company'


@pytest.mark.unit
class TestCompanyGroupValidation:
    def test_required_name_field(self, db_setup):
        with pytest.raises(IntegrityError):
            CompanyGroup.objects.create()

    def test_field_length_constraints(self, db_setup, sample_company):
        long_name = 'a' * 151
        long_description = 'b' * 1001
        long_code_name = 'c' * 151
        
        group = CompanyGroup(
            name=long_name,
            company=sample_company,
            description=long_description,
            code_name=long_code_name
        )
        
        with pytest.raises(ValidationError):
            group.full_clean()

    def test_blank_fields_allowed(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Blank Fields Group',
            company=sample_company,
            description='',
            code_name=''
        )
        
        assert group.description == ''
        assert group.code_name == ''


@pytest.mark.unit
class TestCompanyGroupRelationships:
    def test_company_relationship(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Company Relation Group',
            company=sample_company,
            description='Company relationship test'
        )
        
        assert group.company == sample_company
        assert group in sample_company.company_groups.all()

    def test_user_relationship(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='User Group',
            company=sample_company,
            description='User relationship test'
        )
        
        user1 = User.objects.create_user(
            username='groupuser1',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user2 = User.objects.create_user(
            username='groupuser2',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user1.groups.add(group)
        user2.groups.add(group)
        
        assert user1 in group.users.all()
        assert user2 in group.users.all()
        assert group in user1.groups.all()
        assert group in user2.groups.all()

    def test_permissions_relationship(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Permission Group',
            company=sample_company,
            description='Permission relationship test'
        )
        
        permission = Permission.objects.filter(codename='add_user').first()
        if permission:
            group.permissions.add(permission)
            assert permission in group.permissions.all()

    def test_cascade_deletion_from_company(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Cascade Company',
            code='CASCADE001'
        )
        
        group = CompanyGroup.objects.create(
            name='Cascade Group',
            company=company,
            description='Cascade test'
        )
        
        group_id = group.id
        company.delete()
        
        assert not CompanyGroup.objects.filter(id=group_id).exists()


@pytest.mark.unit
class TestCompanyGroupEdgeCases:
    def test_unicode_and_special_characters(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='测试组 & Group',
            company=sample_company,
            description='Unicode group description with émojis 👥',
            code_name='测试_group'
        )
        
        assert group.name == '测试组 & Group'
        assert group.description == 'Unicode group description with émojis 👥'
        assert group.code_name == '测试_group'

    def test_duplicate_names_different_companies(self, db_setup):
        company1 = CompanyMaster.objects.create(
            name='Company 1',
            code='COMP001'
        )
        
        company2 = CompanyMaster.objects.create(
            name='Company 2',
            code='COMP002'
        )
        
        group1 = CompanyGroup.objects.create(
            name='Duplicate Name',
            company=company1,
            description='First group'
        )
        
        group2 = CompanyGroup.objects.create(
            name='Duplicate Name',
            company=company2,
            description='Second group'
        )
        
        assert group1.name == group2.name
        assert group1.company != group2.company
        assert group1.id != group2.id

    def test_group_without_company_relationships(self, db_setup):
        group = CompanyGroup.objects.create(
            name='Independent Group',
            description='Group without company association'
        )
        
        user = User.objects.create_user(
            username='independentuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user.groups.add(group)
        
        assert group.company is None
        assert user in group.users.all()


@pytest.mark.unit
class TestCompanyGroupMethods:
    def test_group_inheritance_from_django_group(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Django Group Test',
            company=sample_company
        )
        
        assert hasattr(group, 'permissions')
        assert hasattr(group, 'user_set')
        
        permission = Permission.objects.filter(codename='add_user').first()
        if permission:
            group.permissions.add(permission)
            assert group.permissions.count() >= 1

    def test_meta_verbose_names(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Meta Test Group',
            company=sample_company
        )
        
        meta = CompanyGroup._meta
        assert meta.verbose_name == 'company group'
        assert meta.verbose_name_plural == 'company groups'


@pytest.mark.performance
class TestCompanyGroupPerformance:
    def test_bulk_group_creation(self, db_setup, sample_company):
        groups_data = [
            CompanyGroup(
                name=f'Bulk Group {i}',
                company=sample_company,
                description=f'Bulk group {i}',
                code_name=f'bulk_group_{i}'
            )
            for i in range(30)
        ]
        
        created_groups = CompanyGroup.objects.bulk_create(groups_data)
        assert len(created_groups) == 30
        assert CompanyGroup.objects.filter(name__startswith='Bulk Group').count() == 30

    def test_efficient_user_queries(self, db_setup, sample_company):
        groups = []
        for i in range(3):
            group = CompanyGroup.objects.create(
                name=f'Query Group {i}',
                company=sample_company,
                description=f'Query group {i}'
            )
            groups.append(group)
        
        users = []
        for i in range(10):
            user = User.objects.create_user(
                username=f'queryuser_{i}',
                email=f'query_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
            
            group = groups[i % 3]
            user.groups.add(group)
        
        groups_with_users = CompanyGroup.objects.prefetch_related('users').filter(
            name__startswith='Query Group'
        )
        
        assert groups_with_users.count() == 3
        
        total_users = 0
        for group in groups_with_users:
            total_users += group.users.count()
        
        assert total_users == 10

    def test_permission_queries(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Permission Query Group',
            company=sample_company
        )
        
        permissions = Permission.objects.filter(
            content_type__app_label='authz'
        )[:5]
        
        for permission in permissions:
            group.permissions.add(permission)
        
        group_with_permissions = CompanyGroup.objects.prefetch_related(
            'permissions'
        ).get(id=group.id)
        
        assert group_with_permissions.permissions.count() >= 5


@pytest.mark.integration
class TestCompanyGroupIntegration:
    def test_complete_group_lifecycle(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Lifecycle Group',
            company=sample_company,
            description='Initial description',
            code_name='lifecycle_group'
        )
        
        users = []
        for i in range(3):
            user = User.objects.create_user(
                username=f'lifecycleuser_{i}',
                email=f'lifecycle_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
            user.groups.add(group)
        
        permissions = Permission.objects.filter(
            content_type__app_label='authz'
        )[:2]
        
        for permission in permissions:
            group.permissions.add(permission)
        
        assert group.users.count() == 3
        assert group.permissions.count() >= 2
        
        group.description = 'Updated description'
        group.code_name = 'updated_lifecycle_group'
        group.save()
        
        updated_group = CompanyGroup.objects.get(id=group.id)
        assert updated_group.description == 'Updated description'
        assert updated_group.code_name == 'updated_lifecycle_group'
        assert updated_group.updation_date > updated_group.creation_date

    def test_group_with_multiple_companies(self, db_setup):
        companies = []
        for i in range(3):
            company = CompanyMaster.objects.create(
                name=f'Multi Company {i}',
                code=f'MULTI{i:03d}'
            )
            companies.append(company)
        
        groups = []
        for i, company in enumerate(companies):
            for j in range(2):
                group = CompanyGroup.objects.create(
                    name=f'Company {i} Group {j}',
                    company=company,
                    description=f'Group {j} for company {i}'
                )
                groups.append(group)
        
        assert len(groups) == 6
        
        for i, company in enumerate(companies):
            company_groups = CompanyGroup.objects.filter(company=company)
            assert company_groups.count() == 2
            
            for group in company_groups:
                assert group.company == company

    def test_user_group_permissions_integration(self, db_setup, sample_company):
        group = CompanyGroup.objects.create(
            name='Permission Integration Group',
            company=sample_company,
            description='Permission integration test'
        )
        
        user = User.objects.create_user(
            username='permissionuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user.groups.add(group)
        
        permission = Permission.objects.filter(codename='add_user').first()
        if permission:
            group.permissions.add(permission)
            
            user_permissions = user.get_group_permissions()
            assert len(user_permissions) > 0
            
            has_permission = user.has_perm(f'{permission.content_type.app_label}.{permission.codename}')
            assert has_permission is True
