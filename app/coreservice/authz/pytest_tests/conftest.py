import pytest
import os
import django
from django.test import TestCase
from django.db import transaction
from unittest.mock import patch, Mock
from authz.models import CompanyMaster, Facility, User, CompanyGroup
from products.pytest_tests.test_utils import MockTypesenseClient, MockS3Storage

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coreservice.settings')
django.setup()


@pytest.fixture
def db_setup(db):
    pass


@pytest.fixture(autouse=True)
def mock_typesense_client():
    mock_client = MockTypesenseClient()

    with patch('products.typesense.client.get_typesense_client', return_value=mock_client):
        yield mock_client
        mock_client.cleanup_all_records()


@pytest.fixture(autouse=True)
def mock_s3_storage():
    mock_storage = MockS3Storage()
    
    with patch('coreservice.storage_backends.PublicMediaStorage', return_value=mock_storage):
        with patch('coreservice.storage_backends.PrivateMediaStorage', return_value=mock_storage):
            with patch('authz.models.company.PrivateMediaStorage', return_value=mock_storage):
                yield mock_storage


@pytest.fixture
def sample_company(db_setup):
    company = CompanyMaster.objects.create(
        name='Test Company',
        code='TEST001',
        address='123 Test Street',
        city='Test City',
        state='Test State',
        country='Test Country',
        pincode='12345',
        phone='1234567890',
        email='<EMAIL>'
    )
    return company


@pytest.fixture
def sample_facility(db_setup, sample_company):
    facility = Facility.objects.create(
        name='Test Facility',
        code='FAC001',
        description='Test facility for testing',
        facility_type='STORE',
        company=sample_company,
        address='456 Facility Street',
        city='Facility City',
        state='Facility State',
        country='Facility Country',
        pincode='54321',
        latitude='40.7128',
        longitude='-74.0060'
    )
    return facility


class AuthzTestCase(TestCase):
    def setUp(self):
        self.mock_typesense_client = MockTypesenseClient()
        self.mock_s3_storage = MockS3Storage()
        
        self.typesense_patcher = patch(
            'products.typesense.client.get_typesense_client',
            return_value=self.mock_typesense_client
        )
        self.typesense_patcher.start()
        
        self.s3_patcher = patch(
            'coreservice.storage_backends.PublicMediaStorage',
            return_value=self.mock_s3_storage
        )
        self.s3_patcher.start()
        
        self.private_s3_patcher = patch(
            'coreservice.storage_backends.PrivateMediaStorage',
            return_value=self.mock_s3_storage
        )
        self.private_s3_patcher.start()

    def tearDown(self):
        self.typesense_patcher.stop()
        self.s3_patcher.stop()
        self.private_s3_patcher.stop()
        self.mock_typesense_client.cleanup_all_records()


def create_test_company(name='Test Company', code='TEST001'):
    return CompanyMaster.objects.create(
        name=name,
        code=code,
        address='123 Test Street',
        city='Test City',
        state='Test State',
        country='Test Country',
        pincode='12345',
        phone='1234567890',
        email='<EMAIL>'
    )


def create_test_facility(company, name='Test Facility', code='FAC001', facility_type='STORE'):
    return Facility.objects.create(
        name=name,
        code=code,
        description='Test facility',
        facility_type=facility_type,
        company=company,
        address='456 Facility Street',
        city='Facility City',
        state='Facility State',
        country='Facility Country',
        pincode='54321',
        latitude='0.0',
        longitude='0.0'
    )


def create_test_group(company, name='Test Group', code_name='test_group'):
    return CompanyGroup.objects.create(
        name=name,
        company=company,
        description='Test group',
        code_name=code_name
    )
