import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from django.core.files.uploadedfile import SimpleUploadedFile
from unittest.mock import patch, Mock
from authz.models import CompanyMaster, Facility, CompanyGroup
from products.pytest_tests.test_utils import create_test_image, MockS3Storage


@pytest.mark.unit
class TestCompanyMasterModelBasics:
    def test_company_creation(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Test Company',
            code='TEST001',
            address='123 Business Street',
            city='Business City',
            state='Business State',
            country='Business Country',
            pincode='12345',
            phone='1234567890',
            email='<EMAIL>',
            pan_number='**********'
        )
        
        assert company.id is not None
        assert company.name == 'Test Company'
        assert company.code == 'TEST001'
        assert company.address == '123 Business Street'
        assert company.city == 'Business City'
        assert company.state == 'Business State'
        assert company.country == 'Business Country'
        assert company.pincode == '12345'
        assert company.phone == '1234567890'
        assert company.email == '<EMAIL>'
        assert company.pan_number == '**********'
        assert company.creation_date is not None
        assert company.updation_date is not None

    def test_company_default_values(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Default Company',
            code='DEFAULT001'
        )
        
        assert company.address == ''
        assert company.city == ''
        assert company.state == ''
        assert company.country == ''
        assert company.pincode == ''
        assert company.phone == ''
        assert company.email == ''
        assert company.pan_number == ''
        assert company.company_logo == ''


@pytest.mark.unit
class TestCompanyMasterValidation:
    def test_required_fields(self, db_setup):
        with pytest.raises(IntegrityError):
            CompanyMaster.objects.create()
        
        with pytest.raises(IntegrityError):
            CompanyMaster.objects.create(name='Test Company')
        
        with pytest.raises(IntegrityError):
            CompanyMaster.objects.create(code='TEST001')

    def test_field_length_constraints(self, db_setup):
        long_name = 'a' * 101
        long_code = 'b' * 101
        long_address = 'c' * 256
        long_city = 'd' * 101
        long_phone = 'e' * 21
        long_email = 'f' * 101
        long_pan = 'g' * 21
        
        company = CompanyMaster(
            name=long_name,
            code=long_code,
            address=long_address,
            city=long_city,
            phone=long_phone,
            email=long_email,
            pan_number=long_pan
        )
        
        with pytest.raises(ValidationError):
            company.full_clean()

    def test_blank_and_null_fields(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Blank Test Company',
            code='BLANK001',
            address='',
            city='',
            state='',
            country='',
            pincode='',
            phone='',
            email='',
            pan_number=''
        )
        
        assert company.address == ''
        assert company.city == ''
        assert company.state == ''
        assert company.country == ''
        assert company.pincode == ''
        assert company.phone == ''
        assert company.email == ''
        assert company.pan_number == ''


@pytest.mark.unit
class TestCompanyMasterRelationships:
    def test_facilities_relationship(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Facility Company',
            code='FAC001'
        )
        
        facility1 = Facility.objects.create(
            name='Facility 1',
            code='F001',
            description='First facility',
            facility_type='STORE',
            company=company,
            address='Facility Address 1',
            city='Facility City 1',
            state='Facility State 1',
            country='Facility Country 1',
            pincode='11111',
            latitude='0.0',
            longitude='0.0'
        )
        
        facility2 = Facility.objects.create(
            name='Facility 2',
            code='F002',
            description='Second facility',
            facility_type='WAREHOUSE',
            company=company,
            address='Facility Address 2',
            city='Facility City 2',
            state='Facility State 2',
            country='Facility Country 2',
            pincode='22222',
            latitude='1.0',
            longitude='1.0'
        )
        
        assert company.facilities_company.count() == 2
        assert facility1 in company.facilities_company.all()
        assert facility2 in company.facilities_company.all()

    def test_company_groups_relationship(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Group Company',
            code='GROUP001'
        )
        
        group1 = CompanyGroup.objects.create(
            name='Admin Group',
            company=company,
            description='Administrator group'
        )
        
        group2 = CompanyGroup.objects.create(
            name='User Group',
            company=company,
            description='Regular user group'
        )
        
        assert company.company_groups.count() == 2
        assert group1 in company.company_groups.all()
        assert group2 in company.company_groups.all()

    def test_cascade_deletion_behavior(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Cascade Company',
            code='CASCADE001'
        )
        
        facility = Facility.objects.create(
            name='Cascade Facility',
            code='CASC001',
            description='Cascade test facility',
            facility_type='STORE',
            company=company,
            address='Cascade Address',
            city='Cascade City',
            state='Cascade State',
            country='Cascade Country',
            pincode='99999',
            latitude='0.0',
            longitude='0.0'
        )
        
        group = CompanyGroup.objects.create(
            name='Cascade Group',
            company=company
        )
        
        company_id = company.id
        facility_id = facility.id
        group_id = group.id
        
        company.delete()
        
        assert not Facility.objects.filter(id=facility_id).exists()
        assert not CompanyGroup.objects.filter(id=group_id).exists()


@pytest.mark.image_processing
class TestCompanyMasterImageProcessing:
    def test_company_logo_upload(self, db_setup):
        test_image = create_test_image()
        
        with patch('coreservice.storage_backends.PrivateMediaStorage') as mock_storage_class:
            mock_storage = MockS3Storage()
            mock_storage_class.return_value = mock_storage
            
            company = CompanyMaster.objects.create(
                name='Logo Company',
                code='LOGO001',
                company_logo=test_image
            )
            
            assert company.company_logo is not None
            assert 'images/companies/' in company.company_logo.name

    def test_company_logo_cleanup_on_update(self, db_setup):
        test_image1 = create_test_image()
        test_image2 = create_test_image()
        
        with patch('coreservice.storage_backends.PrivateMediaStorage') as mock_storage_class:
            mock_storage = MockS3Storage()
            mock_storage_class.return_value = mock_storage
            
            company = CompanyMaster.objects.create(
                name='Update Logo Company',
                code='UPDATE001',
                company_logo=test_image1
            )
            
            original_logo_name = company.company_logo.name
            
            company.company_logo = test_image2
            company.save()
            
            assert company.company_logo.name != original_logo_name

    def test_company_logo_cleanup_on_delete(self, db_setup):
        test_image = create_test_image()
        
        with patch('coreservice.storage_backends.PrivateMediaStorage') as mock_storage_class:
            mock_storage = MockS3Storage()
            mock_storage_class.return_value = mock_storage
            
            company = CompanyMaster.objects.create(
                name='Delete Logo Company',
                code='DELETE001',
                company_logo=test_image
            )
            
            logo_name = company.company_logo.name
            company.delete()
            
            # In a real scenario, we'd check if the file was deleted from S3
            # Here we just verify the company was deleted
            assert not CompanyMaster.objects.filter(code='DELETE001').exists()


@pytest.mark.unit
class TestCompanyMasterEdgeCases:
    def test_unicode_and_special_characters(self, db_setup):
        company = CompanyMaster.objects.create(
            name='测试公司 & Co.',
            code='测试001',
            address='北京市朝阳区 123号',
            city='北京',
            state='北京市',
            country='中国',
            email='test@测试公司.com'
        )
        
        assert company.name == '测试公司 & Co.'
        assert company.code == '测试001'
        assert company.address == '北京市朝阳区 123号'
        assert company.city == '北京'
        assert company.state == '北京市'
        assert company.country == '中国'

    def test_pan_number_formats(self, db_setup):
        pan_formats = [
            '**********',
            '**********',
            '**********'
        ]
        
        for i, pan in enumerate(pan_formats):
            company = CompanyMaster.objects.create(
                name=f'PAN Company {i}',
                code=f'PAN00{i}',
                pan_number=pan
            )
            
            assert company.pan_number == pan

    def test_email_formats(self, db_setup):
        email_formats = [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
        
        for i, email in enumerate(email_formats):
            company = CompanyMaster.objects.create(
                name=f'Email Company {i}',
                code=f'EMAIL00{i}',
                email=email
            )
            
            assert company.email == email


@pytest.mark.performance
class TestCompanyMasterPerformance:
    def test_bulk_company_creation(self, db_setup):
        companies_data = [
            CompanyMaster(
                name=f'Bulk Company {i}',
                code=f'BULK{i:03d}',
                address=f'Address {i}',
                city=f'City {i}'
            )
            for i in range(50)
        ]
        
        created_companies = CompanyMaster.objects.bulk_create(companies_data)
        assert len(created_companies) == 50
        assert CompanyMaster.objects.filter(name__startswith='Bulk Company').count() == 50

    def test_efficient_facility_queries(self, db_setup):
        companies = []
        for i in range(5):
            company = CompanyMaster.objects.create(
                name=f'Query Company {i}',
                code=f'QUERY{i:03d}'
            )
            companies.append(company)
            
            for j in range(3):
                Facility.objects.create(
                    name=f'Facility {i}-{j}',
                    code=f'FAC{i}{j}',
                    description=f'Facility {i}-{j}',
                    facility_type='STORE',
                    company=company,
                    address=f'Address {i}-{j}',
                    city=f'City {i}-{j}',
                    state=f'State {i}-{j}',
                    country=f'Country {i}-{j}',
                    pincode=f'{i}{j}000',
                    latitude='0.0',
                    longitude='0.0'
                )
        
        companies_with_facilities = CompanyMaster.objects.prefetch_related(
            'facilities_company'
        ).filter(name__startswith='Query Company')
        
        assert companies_with_facilities.count() == 5
        
        for company in companies_with_facilities:
            assert company.facilities_company.count() == 3


@pytest.mark.integration
class TestCompanyMasterIntegration:
    def test_complete_company_lifecycle(self, db_setup):
        test_image = create_test_image()
        
        with patch('coreservice.storage_backends.PrivateMediaStorage') as mock_storage_class:
            mock_storage = MockS3Storage()
            mock_storage_class.return_value = mock_storage
            
            company = CompanyMaster.objects.create(
                name='Lifecycle Company',
                code='LIFECYCLE001',
                company_logo=test_image,
                address='Initial Address',
                city='Initial City',
                email='<EMAIL>'
            )
            
            facility = Facility.objects.create(
                name='Lifecycle Facility',
                code='LIFE001',
                description='Lifecycle facility',
                facility_type='STORE',
                company=company,
                address='Facility Address',
                city='Facility City',
                state='Facility State',
                country='Facility Country',
                pincode='12345',
                latitude='0.0',
                longitude='0.0'
            )
            
            group = CompanyGroup.objects.create(
                name='Lifecycle Group',
                company=company,
                description='Lifecycle group'
            )
            
            assert company.facilities_company.count() == 1
            assert company.company_groups.count() == 1
            
            company.address = 'Updated Address'
            company.city = 'Updated City'
            company.save()
            
            updated_company = CompanyMaster.objects.get(id=company.id)
            assert updated_company.address == 'Updated Address'
            assert updated_company.city == 'Updated City'
            assert updated_company.updation_date > updated_company.creation_date
