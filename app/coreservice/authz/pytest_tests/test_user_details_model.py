import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from datetime import date, datetime
from authz.models import User, UserDetails


@pytest.mark.unit
class TestUserDetailsModelBasics:
    def test_user_details_creation(self, db_setup):
        user = User.objects.create_user(
            username='detailsuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='123 Main Street',
            phone='1234567890',
            date_of_birth=date(1990, 1, 1)
        )
        
        assert user_details.id is not None
        assert user_details.user == user
        assert user_details.address == '123 Main Street'
        assert user_details.phone == '1234567890'
        assert user_details.date_of_birth == date(1990, 1, 1)
        assert str(user_details) == user.username

    def test_user_details_default_values(self, db_setup):
        user = User.objects.create_user(
            username='defaultdetails',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='Test Address',
            phone='1234567890'
        )
        
        assert user_details.date_of_birth is None
        assert user_details.creation_date is not None
        assert user_details.updation_date is not None


@pytest.mark.unit
class TestUserDetailsValidation:
    def test_one_to_one_constraint(self, db_setup):
        user = User.objects.create_user(
            username='uniqueuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        UserDetails.objects.create(
            user=user,
            address='First Address',
            phone='1111111111'
        )
        
        with pytest.raises(IntegrityError):
            UserDetails.objects.create(
                user=user,
                address='Second Address',
                phone='2222222222'
            )

    def test_required_fields_validation(self, db_setup):
        user = User.objects.create_user(
            username='requireduser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        with pytest.raises(IntegrityError):
            UserDetails.objects.create(user=user)

    def test_phone_field_length_validation(self, db_setup):
        user = User.objects.create_user(
            username='phoneuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        long_phone = '1' * 16
        user_details = UserDetails(
            user=user,
            address='Test Address',
            phone=long_phone
        )
        
        with pytest.raises(ValidationError):
            user_details.full_clean()

    def test_address_field_length_validation(self, db_setup):
        user = User.objects.create_user(
            username='addressuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        long_address = 'a' * 101
        user_details = UserDetails(
            user=user,
            address=long_address,
            phone='1234567890'
        )
        
        with pytest.raises(ValidationError):
            user_details.full_clean()


@pytest.mark.unit
class TestUserDetailsRelationships:
    def test_user_relationship(self, db_setup):
        user = User.objects.create_user(
            username='relationuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='Relation Address',
            phone='1234567890'
        )
        
        assert user_details.user == user
        assert user.user_details == user_details

    def test_cascade_deletion_from_user(self, db_setup):
        user = User.objects.create_user(
            username='cascadeuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='Cascade Address',
            phone='1234567890'
        )
        
        user_details_id = user_details.id
        user.delete()
        
        assert not UserDetails.objects.filter(id=user_details_id).exists()


@pytest.mark.unit
class TestUserDetailsEdgeCases:
    def test_date_of_birth_edge_cases(self, db_setup):
        user = User.objects.create_user(
            username='dateuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        test_dates = [
            date(1900, 1, 1),
            date(2023, 12, 31),
            date.today(),
            None
        ]
        
        for i, test_date in enumerate(test_dates):
            test_user = User.objects.create_user(
                username=f'dateuser_{i}',
                email=f'date_{i}@example.com',
                password='testpass123'
            )
            
            user_details = UserDetails.objects.create(
                user=test_user,
                address=f'Address {i}',
                phone=f'123456789{i}',
                date_of_birth=test_date
            )
            
            assert user_details.date_of_birth == test_date

    def test_unicode_and_special_characters(self, db_setup):
        user = User.objects.create_user(
            username='unicodeuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='北京市朝阳区 123号',
            phone='+86-138-0013-8000'
        )
        
        assert user_details.address == '北京市朝阳区 123号'
        assert user_details.phone == '+86-138-0013-8000'

    def test_phone_number_formats(self, db_setup):
        phone_formats = [
            '1234567890',
            '******-567-8900',
            '(*************',
            '************',
            '+91 98765 43210'
        ]
        
        for i, phone in enumerate(phone_formats):
            user = User.objects.create_user(
                username=f'phoneformat_{i}',
                email=f'phoneformat_{i}@example.com',
                password='testpass123'
            )
            
            user_details = UserDetails.objects.create(
                user=user,
                address=f'Address {i}',
                phone=phone
            )
            
            assert user_details.phone == phone


@pytest.mark.unit
class TestUserDetailsQueryOptimization:
    def test_select_related_optimization(self, db_setup):
        users_with_details = []
        
        for i in range(10):
            user = User.objects.create_user(
                username=f'queryuser_{i}',
                email=f'query_{i}@example.com',
                password='testpass123'
            )
            
            UserDetails.objects.create(
                user=user,
                address=f'Address {i}',
                phone=f'123456789{i}'
            )
            
            users_with_details.append(user)
        
        details_with_users = UserDetails.objects.select_related('user').all()
        
        assert details_with_users.count() == 10
        
        for details in details_with_users:
            assert details.user.username.startswith('queryuser_')

    def test_filtering_and_ordering(self, db_setup):
        base_date = date(1990, 1, 1)
        
        for i in range(5):
            user = User.objects.create_user(
                username=f'filteruser_{i}',
                email=f'filter_{i}@example.com',
                password='testpass123'
            )
            
            UserDetails.objects.create(
                user=user,
                address=f'Filter Address {i}',
                phone=f'555000000{i}',
                date_of_birth=date(base_date.year + i, base_date.month, base_date.day)
            )
        
        recent_births = UserDetails.objects.filter(
            date_of_birth__gte=date(1992, 1, 1)
        ).order_by('-date_of_birth')
        
        assert recent_births.count() == 3
        
        dates = [details.date_of_birth for details in recent_births]
        assert dates == sorted(dates, reverse=True)


@pytest.mark.performance
class TestUserDetailsPerformance:
    def test_bulk_user_details_creation(self, db_setup):
        users = []
        for i in range(50):
            user = User.objects.create_user(
                username=f'bulkuser_{i}',
                email=f'bulk_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
        
        user_details_data = [
            UserDetails(
                user=user,
                address=f'Bulk Address {i}',
                phone=f'555{i:07d}'
            )
            for i, user in enumerate(users)
        ]
        
        created_details = UserDetails.objects.bulk_create(user_details_data)
        assert len(created_details) == 50
        assert UserDetails.objects.filter(address__startswith='Bulk Address').count() == 50


@pytest.mark.integration
class TestUserDetailsIntegration:
    def test_complete_user_details_lifecycle(self, db_setup):
        user = User.objects.create_user(
            username='lifecycleuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='Initial Address',
            phone='1111111111',
            date_of_birth=date(1985, 5, 15)
        )
        
        assert user_details.creation_date is not None
        original_creation_date = user_details.creation_date
        
        user_details.address = 'Updated Address'
        user_details.phone = '2222222222'
        user_details.save()
        
        updated_details = UserDetails.objects.get(id=user_details.id)
        assert updated_details.address == 'Updated Address'
        assert updated_details.phone == '2222222222'
        assert updated_details.creation_date == original_creation_date
        assert updated_details.updation_date > original_creation_date

    def test_user_details_with_user_operations(self, db_setup):
        user = User.objects.create_user(
            username='operationsuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        user_details = UserDetails.objects.create(
            user=user,
            address='Operations Address',
            phone='3333333333'
        )
        
        user.first_name = 'John'
        user.last_name = 'Doe'
        user.save()
        
        user_details.refresh_from_db()
        assert user_details.user.first_name == 'John'
        assert user_details.user.last_name == 'Doe'
        
        combined_query = UserDetails.objects.select_related('user').get(
            user__username='operationsuser'
        )
        assert combined_query.user.first_name == 'John'
        assert combined_query.address == 'Operations Address'
