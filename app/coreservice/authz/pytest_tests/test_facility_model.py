import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from authz.models import Facility, CompanyMaster, User, UserFacilityMapping


@pytest.mark.unit
class TestFacilityModelBasics:
    def test_facility_creation(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Test Facility',
            code='TEST001',
            description='Test facility description',
            facility_type='STORE',
            company=sample_company,
            address='123 Facility Street',
            city='Facility City',
            state='Facility State',
            country='Facility Country',
            pincode='12345',
            latitude='40.7128',
            longitude='-74.0060'
        )
        
        assert facility.id is not None
        assert facility.name == 'Test Facility'
        assert facility.code == 'TEST001'
        assert facility.description == 'Test facility description'
        assert facility.facility_type == 'STORE'
        assert facility.company == sample_company
        assert facility.address == '123 Facility Street'
        assert facility.city == 'Facility City'
        assert facility.state == 'Facility State'
        assert facility.country == 'Facility Country'
        assert facility.pincode == '12345'
        assert facility.latitude == '40.7128'
        assert facility.longitude == '-74.0060'
        assert str(facility) == 'Test Facility'
        assert facility.creation_date is not None
        assert facility.updation_date is not None

    def test_facility_tenant_mixin(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Tenant Facility',
            code='TENANT001',
            description='Tenant test facility',
            facility_type='WAREHOUSE',
            company=sample_company,
            address='Tenant Address',
            city='Tenant City',
            state='Tenant State',
            country='Tenant Country',
            pincode='54321',
            latitude='0.0',
            longitude='0.0'
        )
        
        assert hasattr(facility, 'tenant_id')
        assert facility.tenant_id == 'id'


@pytest.mark.unit
class TestFacilityValidation:
    def test_required_fields(self, db_setup, sample_company):
        with pytest.raises(IntegrityError):
            Facility.objects.create()
        
        with pytest.raises(IntegrityError):
            Facility.objects.create(
                name='Incomplete Facility',
                code='INCOMPLETE001'
            )

    def test_facility_type_choices(self, db_setup, sample_company):
        valid_types = ['STORE', 'WAREHOUSE', 'DISTRIBUTION_CENTER']
        
        for facility_type in valid_types:
            facility = Facility.objects.create(
                name=f'{facility_type} Facility',
                code=f'{facility_type}001',
                description=f'{facility_type} description',
                facility_type=facility_type,
                company=sample_company,
                address=f'{facility_type} Address',
                city=f'{facility_type} City',
                state=f'{facility_type} State',
                country=f'{facility_type} Country',
                pincode='12345',
                latitude='0.0',
                longitude='0.0'
            )
            
            assert facility.facility_type == facility_type

    def test_invalid_facility_type(self, db_setup, sample_company):
        facility = Facility(
            name='Invalid Type Facility',
            code='INVALID001',
            description='Invalid type description',
            facility_type='INVALID_TYPE',
            company=sample_company,
            address='Invalid Address',
            city='Invalid City',
            state='Invalid State',
            country='Invalid Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        with pytest.raises(ValidationError):
            facility.full_clean()

    def test_field_length_constraints(self, db_setup, sample_company):
        long_name = 'a' * 101
        long_code = 'b' * 101
        long_address = 'c' * 101
        long_city = 'd' * 101
        long_state = 'e' * 101
        long_country = 'f' * 101
        long_pincode = 'g' * 101
        long_latitude = 'h' * 101
        long_longitude = 'i' * 101
        
        facility = Facility(
            name=long_name,
            code=long_code,
            description='Test description',
            facility_type='STORE',
            company=sample_company,
            address=long_address,
            city=long_city,
            state=long_state,
            country=long_country,
            pincode=long_pincode,
            latitude=long_latitude,
            longitude=long_longitude
        )
        
        with pytest.raises(ValidationError):
            facility.full_clean()


@pytest.mark.unit
class TestFacilityRelationships:
    def test_company_relationship(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Company Facility',
            code='COMPANY001',
            description='Company relationship test',
            facility_type='STORE',
            company=sample_company,
            address='Company Address',
            city='Company City',
            state='Company State',
            country='Company Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        assert facility.company == sample_company
        assert facility in sample_company.facilities_company.all()

    def test_user_facility_mappings(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Mapping Facility',
            code='MAPPING001',
            description='User mapping test',
            facility_type='WAREHOUSE',
            company=sample_company,
            address='Mapping Address',
            city='Mapping City',
            state='Mapping State',
            country='Mapping Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        user = User.objects.create_user(
            username='facilityuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=facility,
            status=0
        )
        
        assert mapping.facility == facility
        assert mapping in facility.user_facility_mappings.all()

    def test_cascade_deletion_from_company(self, db_setup):
        company = CompanyMaster.objects.create(
            name='Cascade Company',
            code='CASCADE001'
        )
        
        facility = Facility.objects.create(
            name='Cascade Facility',
            code='CASCADE_FAC001',
            description='Cascade test',
            facility_type='STORE',
            company=company,
            address='Cascade Address',
            city='Cascade City',
            state='Cascade State',
            country='Cascade Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        facility_id = facility.id
        company.delete()
        
        assert not Facility.objects.filter(id=facility_id).exists()


@pytest.mark.unit
class TestFacilityEdgeCases:
    def test_coordinate_formats(self, db_setup, sample_company):
        coordinate_test_cases = [
            ('0.0', '0.0'),
            ('40.7128', '-74.0060'),
            ('-33.8688', '151.2093'),
            ('90.0', '180.0'),
            ('-90.0', '-180.0')
        ]
        
        for i, (lat, lng) in enumerate(coordinate_test_cases):
            facility = Facility.objects.create(
                name=f'Coordinate Facility {i}',
                code=f'COORD{i:03d}',
                description=f'Coordinate test {i}',
                facility_type='STORE',
                company=sample_company,
                address=f'Address {i}',
                city=f'City {i}',
                state=f'State {i}',
                country=f'Country {i}',
                pincode=f'{i:05d}',
                latitude=lat,
                longitude=lng
            )
            
            assert facility.latitude == lat
            assert facility.longitude == lng

    def test_unicode_and_special_characters(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='测试设施 & Store',
            code='测试001',
            description='Unicode facility description with émojis 🏪',
            facility_type='STORE',
            company=sample_company,
            address='北京市朝阳区 123号',
            city='北京',
            state='北京市',
            country='中国',
            pincode='100000',
            latitude='39.9042',
            longitude='116.4074'
        )
        
        assert facility.name == '测试设施 & Store'
        assert facility.code == '测试001'
        assert facility.address == '北京市朝阳区 123号'
        assert facility.city == '北京'
        assert facility.state == '北京市'
        assert facility.country == '中国'

    def test_pincode_formats(self, db_setup, sample_company):
        pincode_formats = [
            '12345',
            '123456',
            'A1B2C3',
            'SW1A 1AA',
            '10001-1234'
        ]
        
        for i, pincode in enumerate(pincode_formats):
            facility = Facility.objects.create(
                name=f'Pincode Facility {i}',
                code=f'PIN{i:03d}',
                description=f'Pincode test {i}',
                facility_type='WAREHOUSE',
                company=sample_company,
                address=f'Address {i}',
                city=f'City {i}',
                state=f'State {i}',
                country=f'Country {i}',
                pincode=pincode,
                latitude='0.0',
                longitude='0.0'
            )
            
            assert facility.pincode == pincode


@pytest.mark.performance
class TestFacilityPerformance:
    def test_bulk_facility_creation(self, db_setup, sample_company):
        facilities_data = [
            Facility(
                name=f'Bulk Facility {i}',
                code=f'BULK{i:03d}',
                description=f'Bulk facility {i}',
                facility_type='STORE' if i % 2 == 0 else 'WAREHOUSE',
                company=sample_company,
                address=f'Bulk Address {i}',
                city=f'Bulk City {i}',
                state=f'Bulk State {i}',
                country=f'Bulk Country {i}',
                pincode=f'{i:05d}',
                latitude=str(float(i)),
                longitude=str(float(i))
            )
            for i in range(50)
        ]
        
        created_facilities = Facility.objects.bulk_create(facilities_data)
        assert len(created_facilities) == 50
        assert Facility.objects.filter(name__startswith='Bulk Facility').count() == 50

    def test_efficient_company_queries(self, db_setup):
        companies = []
        for i in range(3):
            company = CompanyMaster.objects.create(
                name=f'Query Company {i}',
                code=f'QCOMP{i:03d}'
            )
            companies.append(company)
            
            for j in range(5):
                Facility.objects.create(
                    name=f'Query Facility {i}-{j}',
                    code=f'QFAC{i}{j}',
                    description=f'Query facility {i}-{j}',
                    facility_type='STORE',
                    company=company,
                    address=f'Query Address {i}-{j}',
                    city=f'Query City {i}-{j}',
                    state=f'Query State {i}-{j}',
                    country=f'Query Country {i}-{j}',
                    pincode=f'{i}{j}000',
                    latitude='0.0',
                    longitude='0.0'
                )
        
        facilities_with_companies = Facility.objects.select_related('company').filter(
            name__startswith='Query Facility'
        )
        
        assert facilities_with_companies.count() == 15
        
        for facility in facilities_with_companies:
            assert facility.company.name.startswith('Query Company')


@pytest.mark.integration
class TestFacilityIntegration:
    def test_complete_facility_lifecycle(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Lifecycle Facility',
            code='LIFECYCLE001',
            description='Lifecycle test facility',
            facility_type='STORE',
            company=sample_company,
            address='Initial Address',
            city='Initial City',
            state='Initial State',
            country='Initial Country',
            pincode='00000',
            latitude='0.0',
            longitude='0.0'
        )
        
        user = User.objects.create_user(
            username='facilityuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=facility,
            status=0
        )
        
        assert facility.user_facility_mappings.count() == 1
        assert mapping.facility == facility
        
        facility.address = 'Updated Address'
        facility.city = 'Updated City'
        facility.facility_type = 'WAREHOUSE'
        facility.save()
        
        updated_facility = Facility.objects.get(id=facility.id)
        assert updated_facility.address == 'Updated Address'
        assert updated_facility.city == 'Updated City'
        assert updated_facility.facility_type == 'WAREHOUSE'
        assert updated_facility.updation_date > updated_facility.creation_date

    def test_facility_with_multiple_users(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Multi User Facility',
            code='MULTI001',
            description='Multi user test',
            facility_type='DISTRIBUTION_CENTER',
            company=sample_company,
            address='Multi Address',
            city='Multi City',
            state='Multi State',
            country='Multi Country',
            pincode='99999',
            latitude='0.0',
            longitude='0.0'
        )
        
        users = []
        for i in range(5):
            user = User.objects.create_user(
                username=f'multiuser_{i}',
                email=f'multi_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
            
            UserFacilityMapping.objects.create(
                user=user,
                facility=facility,
                status=0 if i % 2 == 0 else 1
            )
        
        assert facility.user_facility_mappings.count() == 5
        
        active_mappings = facility.user_facility_mappings.filter(status=0)
        inactive_mappings = facility.user_facility_mappings.filter(status=1)
        
        assert active_mappings.count() == 3
        assert inactive_mappings.count() == 2

    def test_facility_filtering_and_aggregation(self, db_setup, sample_company):
        facility_types = ['STORE', 'WAREHOUSE', 'DISTRIBUTION_CENTER']
        
        for i in range(15):
            Facility.objects.create(
                name=f'Filter Facility {i}',
                code=f'FILTER{i:03d}',
                description=f'Filter test {i}',
                facility_type=facility_types[i % 3],
                company=sample_company,
                address=f'Filter Address {i}',
                city=f'Filter City {i}',
                state=f'Filter State {i}',
                country=f'Filter Country {i}',
                pincode=f'{i:05d}',
                latitude=str(float(i)),
                longitude=str(float(i))
            )
        
        stores = Facility.objects.filter(facility_type='STORE')
        warehouses = Facility.objects.filter(facility_type='WAREHOUSE')
        distribution_centers = Facility.objects.filter(facility_type='DISTRIBUTION_CENTER')
        
        assert stores.count() == 5
        assert warehouses.count() == 5
        assert distribution_centers.count() == 5
        
        facilities_by_company = Facility.objects.filter(
            company=sample_company,
            name__startswith='Filter Facility'
        ).order_by('name')
        
        assert facilities_by_company.count() == 15
        
        facility_names = [f.name for f in facilities_by_company]
        assert facility_names == sorted(facility_names)
