import pytest
from django.db import IntegrityError
from django.core.exceptions import ValidationError
from authz.models import UserFacilityMapping, User, Facility, CompanyMaster


@pytest.mark.unit
class TestUserFacilityMappingModelBasics:
    def test_mapping_creation(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='mappinguser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility,
            status=0
        )
        
        assert mapping.id is not None
        assert mapping.user == user
        assert mapping.facility == sample_facility
        assert mapping.status == 0
        assert mapping.creation_date is not None
        assert mapping.updation_date is not None
        assert str(mapping) == f"{user} - {sample_facility}"

    def test_mapping_default_values(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='defaultmapping',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility
        )
        
        assert mapping.status == 0
        assert mapping.creation_date is not None
        assert mapping.updation_date is not None


@pytest.mark.unit
class TestUserFacilityMappingValidation:
    def test_required_fields(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='requireduser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        with pytest.raises(IntegrityError):
            UserFacilityMapping.objects.create()
        
        with pytest.raises(IntegrityError):
            UserFacilityMapping.objects.create(user=user)
        
        with pytest.raises(IntegrityError):
            UserFacilityMapping.objects.create(facility=sample_facility)

    def test_status_choices(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='statususer',
            email='<EMAIL>',
            password='testpass123'
        )
        
        valid_statuses = [0, 1]
        
        for status in valid_statuses:
            mapping = UserFacilityMapping.objects.create(
                user=user,
                facility=sample_facility,
                status=status
            )
            assert mapping.status == status
            
            mapping.delete()

    def test_unique_together_constraint(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='uniqueuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility,
            status=0
        )
        
        with pytest.raises(IntegrityError):
            UserFacilityMapping.objects.create(
                user=user,
                facility=sample_facility,
                status=1
            )


@pytest.mark.unit
class TestUserFacilityMappingRelationships:
    def test_user_relationship(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='relationuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility,
            status=0
        )
        
        assert mapping.user == user
        assert mapping in user.user_facility_mappings.all()

    def test_facility_relationship(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Relation Facility',
            code='REL001',
            description='Relation test facility',
            facility_type='STORE',
            company=sample_company,
            address='Relation Address',
            city='Relation City',
            state='Relation State',
            country='Relation Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        user = User.objects.create_user(
            username='facilityrelation',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=facility,
            status=0
        )
        
        assert mapping.facility == facility
        assert mapping in facility.user_facility_mappings.all()

    def test_cascade_deletion_from_user(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='cascadeuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility,
            status=0
        )
        
        mapping_id = mapping.id
        user.delete()
        
        assert not UserFacilityMapping.objects.filter(id=mapping_id).exists()

    def test_cascade_deletion_from_facility(self, db_setup, sample_company):
        facility = Facility.objects.create(
            name='Cascade Facility',
            code='CASCADE001',
            description='Cascade test',
            facility_type='STORE',
            company=sample_company,
            address='Cascade Address',
            city='Cascade City',
            state='Cascade State',
            country='Cascade Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        user = User.objects.create_user(
            username='facilitycascade',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=facility,
            status=0
        )
        
        mapping_id = mapping.id
        facility.delete()
        
        assert not UserFacilityMapping.objects.filter(id=mapping_id).exists()


@pytest.mark.unit
class TestUserFacilityMappingEdgeCases:
    def test_multiple_facilities_per_user(self, db_setup, sample_company):
        user = User.objects.create_user(
            username='multiuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        facilities = []
        for i in range(3):
            facility = Facility.objects.create(
                name=f'Multi Facility {i}',
                code=f'MULTI{i:03d}',
                description=f'Multi facility {i}',
                facility_type='STORE',
                company=sample_company,
                address=f'Multi Address {i}',
                city=f'Multi City {i}',
                state=f'Multi State {i}',
                country=f'Multi Country {i}',
                pincode=f'{i:05d}',
                latitude='0.0',
                longitude='0.0'
            )
            facilities.append(facility)
            
            UserFacilityMapping.objects.create(
                user=user,
                facility=facility,
                status=0 if i % 2 == 0 else 1
            )
        
        assert user.user_facility_mappings.count() == 3
        
        active_mappings = user.user_facility_mappings.filter(status=0)
        inactive_mappings = user.user_facility_mappings.filter(status=1)
        
        assert active_mappings.count() == 2
        assert inactive_mappings.count() == 1

    def test_multiple_users_per_facility(self, db_setup, sample_facility):
        users = []
        for i in range(5):
            user = User.objects.create_user(
                username=f'facilityuser_{i}',
                email=f'facilityuser_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
            
            UserFacilityMapping.objects.create(
                user=user,
                facility=sample_facility,
                status=0 if i < 3 else 1
            )
        
        assert sample_facility.user_facility_mappings.count() == 5
        
        active_mappings = sample_facility.user_facility_mappings.filter(status=0)
        inactive_mappings = sample_facility.user_facility_mappings.filter(status=1)
        
        assert active_mappings.count() == 3
        assert inactive_mappings.count() == 2

    def test_status_transitions(self, db_setup, sample_facility):
        user = User.objects.create_user(
            username='transitionuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=sample_facility,
            status=0
        )
        
        assert mapping.status == 0
        
        mapping.status = 1
        mapping.save()
        
        updated_mapping = UserFacilityMapping.objects.get(id=mapping.id)
        assert updated_mapping.status == 1
        assert updated_mapping.updation_date > updated_mapping.creation_date


@pytest.mark.performance
class TestUserFacilityMappingPerformance:
    def test_bulk_mapping_creation(self, db_setup, sample_company):
        users = []
        for i in range(10):
            user = User.objects.create_user(
                username=f'bulkuser_{i}',
                email=f'bulk_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
        
        facilities = []
        for i in range(5):
            facility = Facility.objects.create(
                name=f'Bulk Facility {i}',
                code=f'BULK{i:03d}',
                description=f'Bulk facility {i}',
                facility_type='STORE',
                company=sample_company,
                address=f'Bulk Address {i}',
                city=f'Bulk City {i}',
                state=f'Bulk State {i}',
                country=f'Bulk Country {i}',
                pincode=f'{i:05d}',
                latitude='0.0',
                longitude='0.0'
            )
            facilities.append(facility)
        
        mappings_data = []
        for user in users:
            for facility in facilities:
                mappings_data.append(
                    UserFacilityMapping(
                        user=user,
                        facility=facility,
                        status=0
                    )
                )
        
        created_mappings = UserFacilityMapping.objects.bulk_create(mappings_data)
        assert len(created_mappings) == 50
        assert UserFacilityMapping.objects.count() == 50

    def test_efficient_queries(self, db_setup, sample_company):
        users = []
        for i in range(5):
            user = User.objects.create_user(
                username=f'queryuser_{i}',
                email=f'query_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
        
        facilities = []
        for i in range(3):
            facility = Facility.objects.create(
                name=f'Query Facility {i}',
                code=f'QUERY{i:03d}',
                description=f'Query facility {i}',
                facility_type='STORE',
                company=sample_company,
                address=f'Query Address {i}',
                city=f'Query City {i}',
                state=f'Query State {i}',
                country=f'Query Country {i}',
                pincode=f'{i:05d}',
                latitude='0.0',
                longitude='0.0'
            )
            facilities.append(facility)
        
        for user in users:
            for facility in facilities:
                UserFacilityMapping.objects.create(
                    user=user,
                    facility=facility,
                    status=0
                )
        
        mappings_with_relations = UserFacilityMapping.objects.select_related(
            'user', 'facility'
        ).filter(status=0)
        
        assert mappings_with_relations.count() == 15
        
        for mapping in mappings_with_relations:
            assert mapping.user.username.startswith('queryuser_')
            assert mapping.facility.name.startswith('Query Facility')


@pytest.mark.integration
class TestUserFacilityMappingIntegration:
    def test_complete_mapping_lifecycle(self, db_setup, sample_company):
        user = User.objects.create_user(
            username='lifecycleuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        facility = Facility.objects.create(
            name='Lifecycle Facility',
            code='LIFECYCLE001',
            description='Lifecycle test',
            facility_type='STORE',
            company=sample_company,
            address='Lifecycle Address',
            city='Lifecycle City',
            state='Lifecycle State',
            country='Lifecycle Country',
            pincode='12345',
            latitude='0.0',
            longitude='0.0'
        )
        
        mapping = UserFacilityMapping.objects.create(
            user=user,
            facility=facility,
            status=0
        )
        
        assert mapping.status == 0
        assert mapping.creation_date is not None
        original_creation_date = mapping.creation_date
        
        mapping.status = 1
        mapping.save()
        
        updated_mapping = UserFacilityMapping.objects.get(id=mapping.id)
        assert updated_mapping.status == 1
        assert updated_mapping.creation_date == original_creation_date
        assert updated_mapping.updation_date > original_creation_date

    def test_complex_user_facility_relationships(self, db_setup, sample_company):
        users = []
        for i in range(3):
            user = User.objects.create_user(
                username=f'complexuser_{i}',
                email=f'complex_{i}@example.com',
                password='testpass123'
            )
            users.append(user)
        
        facilities = []
        for i in range(4):
            facility = Facility.objects.create(
                name=f'Complex Facility {i}',
                code=f'COMPLEX{i:03d}',
                description=f'Complex facility {i}',
                facility_type='STORE' if i % 2 == 0 else 'WAREHOUSE',
                company=sample_company,
                address=f'Complex Address {i}',
                city=f'Complex City {i}',
                state=f'Complex State {i}',
                country=f'Complex Country {i}',
                pincode=f'{i:05d}',
                latitude='0.0',
                longitude='0.0'
            )
            facilities.append(facility)
        
        for i, user in enumerate(users):
            for j, facility in enumerate(facilities):
                if (i + j) % 2 == 0:
                    UserFacilityMapping.objects.create(
                        user=user,
                        facility=facility,
                        status=0 if (i + j) % 3 == 0 else 1
                    )
        
        total_mappings = UserFacilityMapping.objects.count()
        active_mappings = UserFacilityMapping.objects.filter(status=0).count()
        inactive_mappings = UserFacilityMapping.objects.filter(status=1).count()
        
        assert total_mappings == active_mappings + inactive_mappings
        assert total_mappings > 0
        
        for user in users:
            user_mappings = user.user_facility_mappings.all()
            assert user_mappings.count() > 0
        
        for facility in facilities:
            facility_mappings = facility.user_facility_mappings.all()
            # Some facilities might not have mappings due to the (i + j) % 2 == 0 condition
