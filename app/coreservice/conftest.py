import os
import pytest
import django
from django.conf import settings
from django.test import override_settings
from django.db import transaction
from unittest.mock import Mock, patch
from io import BytesIO
from PIL import Image

# Configure Django settings for testing
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'coreservice.settings')
django.setup()

from authz.models import User, Facility, CompanyMaster
from products.models import Category, Subcategory


@pytest.fixture(scope='session')
def django_db_setup():
    """Setup test database"""
    settings.DATABASES['default'] = {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': ':memory:',
    }


@pytest.fixture
def db_setup(db):
    """Setup database with initial data"""
    with transaction.atomic():
        company = CompanyMaster.objects.create(
            name="Test Company", code="TEST_COMP", description="Test company for testing"
        )
        
        facility = Facility.objects.create(
            name="Test Facility", code="TEST_FAC", description="Test facility for testing",
            facility_type="STORE", company=company, address="123 Test St",
            city="Test City", state="Test State", country="Test Country",
            pincode="12345", latitude="0.0", longitude="0.0"
        )
        
        user = User.objects.create_user(
            username="testuser", email="<EMAIL>", 
            password="testpass123", user_type="sub_user"
        )
        
        return {'company': company, 'facility': facility, 'user': user}


@pytest.fixture
def mock_s3_storage():
    """Mock S3 storage to avoid external dependencies"""
    with patch('coreservice.storage_backends.CategoryImageStorage') as mock_class:
        mock_storage = Mock()
        mock_class.return_value = mock_storage
        
        # Configure basic mock methods
        mock_storage.save.return_value = "test_image.jpg"
        mock_storage.url.return_value = "https://test-bucket.s3.amazonaws.com/test_image.jpg"
        
        # Create test image
        test_image = Image.new('RGB', (100, 100), color='red')
        test_image_io = BytesIO()
        test_image.save(test_image_io, format='JPEG')
        test_image_io.seek(0)
        mock_storage.open.return_value = test_image_io
        
        yield mock_storage


@pytest.fixture
def mock_typesense_client():
    """Mock Typesense client"""
    with patch('products.typesense.client.get_typesense_client') as mock_func:
        mock_client = Mock()
        mock_collection = Mock()
        mock_documents = Mock()
        
        # Setup mock chain
        mock_client.collections = {
            'categories': mock_collection, 
            'subcategories': mock_collection, 
            'products': mock_collection
        }
        mock_collection.documents = mock_documents
        mock_documents.upsert.return_value = {'id': 'test-id'}
        mock_documents.delete.return_value = {'id': 'test-id'}
        
        mock_func.return_value = mock_client
        yield mock_client


@pytest.fixture(autouse=True)
def mock_typesense_globally():
    """Automatically mock Typesense client for ALL tests to prevent real Typesense usage"""
    from products.pytest_tests.test_utils import MockTypesenseClient

    with patch('products.typesense.client.get_typesense_client') as mock_func:
        mock_client = MockTypesenseClient()
        mock_func.return_value = mock_client

        # Yield the mock client for test use
        yield mock_client

        # Cleanup all records after test completes
        mock_client.cleanup_all_records()


@pytest.fixture
def typesense_mock(mock_typesense_globally):
    """Convenient access to the globally mocked Typesense client"""
    return mock_typesense_globally


@pytest.fixture
def mock_typesense_with_cleanup():
    """Mock Typesense client with automatic cleanup after each test (deprecated - use mock_typesense_globally)"""
    from products.pytest_tests.test_utils import MockTypesenseClient

    with patch('products.typesense.client.get_typesense_client') as mock_func:
        mock_client = MockTypesenseClient()
        mock_func.return_value = mock_client

        # Yield the mock client for test use
        yield mock_client

        # Cleanup all records after test completes
        mock_client.cleanup_all_records()


@pytest.fixture
def create_test_image():
    """Create a test image file for upload testing"""
    def _create_image(width=100, height=100, color='red', format='JPEG'):
        image = Image.new('RGB', (width, height), color=color)
        image_io = BytesIO()
        image.save(image_io, format=format)
        image_io.seek(0)
        image_io.name = f'test_image.{format.lower()}'
        return image_io
    return _create_image


@pytest.fixture
def sample_category_data():
    """Sample category data for testing"""
    return {
        'name': 'Electronics',
        'description': 'Electronic devices and gadgets',
        'code': 'ELEC001',
        'is_active': True,
        'thumbnail_url': 'https://example.com/electronics.jpg',
        'section': 'main'
    }


@pytest.fixture
def sample_subcategory_data():
    """Sample subcategory data for testing"""
    return {
        'name': 'Smartphones',
        'description': 'Mobile phones and accessories',
        'code': 'SMART001',
        'is_active': True,
        'thumbnail_url': 'https://example.com/smartphones.jpg'
    }


@pytest.fixture
def mock_image_processing():
    """Mock image processing functions"""
    from products.pytest_tests.test_utils import MockImageProcessing
    return MockImageProcessing()


@pytest.fixture
def test_settings():
    """Override settings for testing"""
    with override_settings(
        USE_S3=False,
        TYPESENSE_API_KEY='test-key',
        TYPESENSE_HOST='localhost',
        TYPESENSE_PORT='8108',
        TYPESENSE_PROTOCOL='http',
        MEDIA_ROOT='/tmp/test_media',
        DEFAULT_FILE_STORAGE='django.core.files.storage.FileSystemStorage'
    ):
        yield
